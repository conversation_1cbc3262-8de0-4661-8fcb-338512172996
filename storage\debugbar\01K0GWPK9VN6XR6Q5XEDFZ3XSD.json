{"__meta": {"id": "01K0GWPK9VN6XR6Q5XEDFZ3XSD", "datetime": "2025-07-19 15:27:08", "utime": ********28.475581, "method": "GET", "uri": "/internal/invoices?activeTab=3", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": ********26.798917, "end": ********28.4756, "duration": 1.676682949066162, "duration_str": "1.68s", "measures": [{"label": "Booting", "start": ********26.798917, "relative_start": 0, "end": **********.095318, "relative_end": **********.095318, "duration": 0.****************, "duration_str": "296ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.095344, "relative_start": 0.*****************, "end": ********28.475602, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.578955, "relative_start": 0.****************, "end": **********.579831, "relative_end": **********.579831, "duration": 0.0008759498596191406, "duration_str": "876μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.786846, "relative_start": 0.****************, "end": **********.786846, "relative_end": **********.786846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.846285, "relative_start": 1.***************, "end": **********.846285, "relative_end": **********.846285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.900613, "relative_start": 1.1016960144042969, "end": **********.900613, "relative_end": **********.900613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.951078, "relative_start": 1.152160882949829, "end": **********.951078, "relative_end": **********.951078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": ********28.002891, "relative_start": 1.2039740085601807, "end": ********28.002891, "relative_end": ********28.002891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": ********28.066786, "relative_start": 1.267868995666504, "end": ********28.066786, "relative_end": ********28.066786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": ********28.123799, "relative_start": 1.3248820304870605, "end": ********28.123799, "relative_end": ********28.123799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": ********28.134563, "relative_start": 1.3356459140777588, "end": ********28.134563, "relative_end": ********28.134563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": ********28.195778, "relative_start": 1.3968608379364014, "end": ********28.195778, "relative_end": ********28.195778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e93d402ed1f3da8a07f4840136a03cb", "start": ********28.288765, "relative_start": 1.4898478984832764, "end": ********28.288765, "relative_end": ********28.288765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": ********28.473708, "relative_start": 1.674790859222412, "end": ********28.473798, "relative_end": ********28.473798, "duration": 9.012222290039062e-05, "duration_str": "90μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": ********28.475151, "relative_start": 1.6762340068817139, "end": ********28.475235, "relative_end": ********28.475235, "duration": 8.392333984375e-05, "duration_str": "84μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 10816528, "peak_usage_str": "10MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.18", "Environment": "local", "Debug Mode": "Enabled", "URL": "dev.psp.com", "Timezone": "Asia/Jakarta", "Locale": "en"}}, "views": {"count": 10, "nb_templates": 10, "templates": [{"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.7868, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.846215, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.900535, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.951042, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": ********28.002852, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": ********28.066746, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": ********28.12373, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "livewire::tailwind", "param_count": null, "params": [], "start": ********28.134524, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": ********28.195716, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::5e93d402ed1f3da8a07f4840136a03cb", "param_count": null, "params": [], "start": ********28.288719, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/5e93d402ed1f3da8a07f4840136a03cb.blade.php__components::5e93d402ed1f3da8a07f4840136a03cb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F5e93d402ed1f3da8a07f4840136a03cb.blade.php&line=1", "ajax": false, "filename": "5e93d402ed1f3da8a07f4840136a03cb.blade.php", "line": "?"}}]}, "queries": {"count": 18, "nb_statements": 18, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.053599999999999995, "accumulated_duration_str": "53.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 5 and `user_type` = 'admin' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5846791, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "psp_dev", "explain": null, "start_percent": 0, "width_percent": 5.41}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'pending' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.6206532, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 5.41, "width_percent": 6.716}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'processing' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["processing"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.6283672, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 12.127, "width_percent": 4.478}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'paid' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.633222, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 16.604, "width_percent": 4.198}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'failed' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["failed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.638342, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 20.802, "width_percent": 4.963}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'expired' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["expired"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.643403, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 25.765, "width_percent": 3.918}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'cancelled' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["cancelled"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.6478179, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 29.683, "width_percent": 5.037}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'refunded' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["refunded"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.653276, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 34.72, "width_percent": 4.366}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'paid' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.687325, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "psp_dev", "explain": null, "start_percent": 39.086, "width_percent": 5.205}, {"sql": "select * from `invoices` where `payment_status` = 'paid' and `invoices`.`deleted_at` is null order by `invoice_date` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.693018, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 44.291, "width_percent": 5.019}, {"sql": "select * from `users` where `users`.`id` in (4) and `user_type` = 'user' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["user"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.699877, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 49.31, "width_percent": 4.981}, {"sql": "select * from `subscriptions` where `subscriptions`.`id` = 83 and `subscriptions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [83], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "D:\\WebData\\www\\psp\\app\\Models\\Invoice.php", "line": 135}, {"index": 31, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 80}, {"index": 32, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeCopied.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeCopied.php", "line": 18}, {"index": 33, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 2142}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.749688, "duration": 0.00315, "duration_str": "3.15ms", "memory": 0, "memory_str": null, "filename": "Invoice.php:135", "source": {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "D:\\WebData\\www\\psp\\app\\Models\\Invoice.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FInvoice.php&line=135", "ajax": false, "filename": "Invoice.php", "line": "135"}, "connection": "psp_dev", "explain": null, "start_percent": 54.291, "width_percent": 5.877}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (5) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\UserInternal'", "type": "query", "params": [], "bindings": ["App\\Models\\UserInternal"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": ********28.207817, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "psp_dev", "explain": null, "start_percent": 60.168, "width_percent": 6.791}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (5) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\UserInternal'", "type": "query", "params": [], "bindings": ["App\\Models\\UserInternal"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": ********28.217122, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "psp_dev", "explain": null, "start_percent": 66.959, "width_percent": 6.418}, {"sql": "select count(*) as aggregate from `tickets` where `status` = 'open' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": ********28.251705, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "TicketResource.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FTicketResource.php&line=33", "ajax": false, "filename": "TicketResource.php", "line": "33"}, "connection": "psp_dev", "explain": null, "start_percent": 73.377, "width_percent": 6.847}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": ********28.272298, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "psp_dev", "explain": null, "start_percent": 80.224, "width_percent": 7.052}, {"sql": "select count(*) as aggregate from `tickets` where `status` = 'open' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": ********28.3524299, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "TicketResource.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FTicketResource.php&line=33", "ajax": false, "filename": "TicketResource.php", "line": "33"}, "connection": "psp_dev", "explain": null, "start_percent": 87.276, "width_percent": 4.72}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": ********28.36809, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "psp_dev", "explain": null, "start_percent": 91.996, "width_percent": 8.004}]}, "models": {"data": {"App\\Models\\Invoice": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "App\\Models\\UserInternal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserInternal.php&line=1", "ajax": false, "filename": "UserInternal.php", "line": "?"}}, "App\\Models\\UserMember": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserMember.php&line=1", "ajax": false, "filename": "UserMember.php", "line": "?"}}, "App\\Models\\Subscription": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FSubscription.php&line=1", "ajax": false, "filename": "Subscription.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 11, "is_counter": true}, "livewire": {"data": {"app.filament.internal.resources.invoice-resource.pages.list-invoices #fe9RSywXkKqujypvIKfC": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => null\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => \"3\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.internal.resources.invoice-resource.pages.list-invoices\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices\"\n  \"id\" => \"fe9RSywXkKqujypvIKfC\"\n]", "filament.livewire.notifications #e91wQ5BvLq6RDzl4zfuX": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#5436\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"e91wQ5BvLq6RDzl4zfuX\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 20, "messages": [{"message": "[\n  ability => page_Product,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-947230541 data-indent-pad=\"  \"><span class=sf-dump-note>page_Product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">page_Product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-947230541\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.224916, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1425712492 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425712492\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.233905, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-321262347 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-321262347\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.235162, "xdebug_link": null}, {"message": "[\n  ability => view_any_post::category,\n  target => null,\n  result => null,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1101904300 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post::category </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_post::category</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101904300\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": ********28.237543, "xdebug_link": null}, {"message": "[\n  ability => view_any_post,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-143978098 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_post</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143978098\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.238649, "xdebug_link": null}, {"message": "[\n  ability => view_any_regulation,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-158144034 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_regulation </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">view_any_regulation</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-158144034\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.243178, "xdebug_link": null}, {"message": "[\n  ability => view_any_setting,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-147707141 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147707141\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.24542, "xdebug_link": null}, {"message": "[\n  ability => view_any_userinternal,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-115527212 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_userinternal </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_userinternal</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-115527212\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.261315, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1772208412 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772208412\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.269592, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user_internal => 5,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1317863168 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1317863168\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.270557, "xdebug_link": null}, {"message": "[\n  ability => page_Product,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-14304976 data-indent-pad=\"  \"><span class=sf-dump-note>page_Product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">page_Product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14304976\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.329054, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-416705928 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416705928\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.334849, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2026156759 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2026156759\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.336519, "xdebug_link": null}, {"message": "[\n  ability => view_any_post::category,\n  target => null,\n  result => null,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1648518781 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post::category </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_post::category</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1648518781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": ********28.339718, "xdebug_link": null}, {"message": "[\n  ability => view_any_post,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-146539004 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_post</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-146539004\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.34131, "xdebug_link": null}, {"message": "[\n  ability => view_any_regulation,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1149680351 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_regulation </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">view_any_regulation</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1149680351\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.344987, "xdebug_link": null}, {"message": "[\n  ability => view_any_setting,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-943808205 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-943808205\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.347422, "xdebug_link": null}, {"message": "[\n  ability => view_any_userinternal,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2114322912 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_userinternal </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_userinternal</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2114322912\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.360244, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1495319121 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495319121\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.364309, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user_internal => 5,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-765952284 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-765952284\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********28.36558, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://dev.psp.com/internal/invoices?activeTab=3", "action_name": "filament.internal.resources.invoices.index", "controller_action": "App\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices", "uri": "GET internal/invoices", "controller": "App\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices@render<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "internal/invoices", "file": "<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:internal, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "telescope": "<a href=\"https://dev.psp.com/_debugbar/telescope/9f6d336a-2d39-41e5-aa4f-ebf5204ace6d\" target=\"_blank\">View in Telescope</a>", "duration": "1.68s", "peak_memory": "16MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>activeTab</span>\" => \"<span class=sf-dump-str>3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1329192262 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1695 characters\">remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5QMVd4eVAzYWVDZTJPbU1IOC9lVUE9PSIsInZhbHVlIjoiOXJ2dHFYRUd1WU1TRis0Njg2VHRKV3FlTUhPR3ZOZ0FMSzVTdG9SK2RWTCsvOEViMEFCbTdBdHRTc1h4WXZSb0lvU2x2MTBFODBCaFREVlBScW9zSkhjR2xURktSMjFaUW9sYUNFVXBkUkRLQmdrVkZtQzJIOXBFVlpCVnNTY1BvNWs0YWtwMGxKS3VNdTIvQldFZWdnPT0iLCJtYWMiOiI3ODdkN2ExYmExZjU5MzE2ODBmZTU4NmRhNmQxMDVjNTUxMWE3ODEyY2EzYWVkNTU0NWUwOTk4ZmI2YjczNDdjIiwidGFnIjoiIn0%3D; remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IitDNnNWWlM0T0JuVitIL2ptLzY3WWc9PSIsInZhbHVlIjoiYkRZRFJ5MlMxbFBXL2dqNzlzQmwvQW9HVk11N3hYS3IxMHZZMkZCWWhjWko3N1kvbDYvTGU2UDZ0NE1vbHRBUUwrdUFSam15akJlVjdvbVBGWXFLVFUxVVlBWUM2UkxNVmgyWGVZU2NnL21RMjJybUZuZzFOTFN1Z3RVRUNobCtiNjJmOEs4TWZ0NGM1bittb3ZaLzNFZFVvTTRycVM5RHBLaGsyUThNVzZqdFNiYnBldVV0TWNZLzRocjcxUGZ0UUhBaG5RWXlIdEU0d3RnQUZMZVAwTldqMHJyYVBlZ0lrQlphOHNuWVpBQT0iLCJtYWMiOiI1ZjhkMDMzYzRiMzAyOWFmZWJhNThmYzYwODU3Yjc2N2NkYTg1OWJmZTkxNzUwYWVhMjg0ODQzODMxNDlkOTg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ii96aG14czJONG5lbHZRZ3AzNjNWTXc9PSIsInZhbHVlIjoiTllCMzB2eTVnYWx1ZVZRcWVpK2FlYnFhd3ZnUTNwLzNaR0dvSWRrY1hLTFFqSGwzTFBDMThKY1lKbjZ3Qm1KVncrY0VOU3Y3b0tjb2NuS24xb2l1NzJJaHZROGM1ZnkvMGx0OHNyL1RUTDJDNkhIQ0VhWU1IQlNkc0lzTUE1UWYiLCJtYWMiOiI5YWJiNTdmNTJmMDk4YWY2NTMxNjQ1YjQ2NWRhOTEzMzFmNjc3NjE5ZWYzYTZjOWFlZGU4M2M4NGU5NjkzZWI5IiwidGFnIjoiIn0%3D; ceval_app_session=eyJpdiI6IkRBNU5uTW9YOHlKTkJpakI0WWlwU1E9PSIsInZhbHVlIjoidC9KNUpuNlA5RUx4RUgwaWYwZnYyOURxQVZpRllZSVhsbDhhZmRGck83SG9ndlV0RkVweVdyTVEwNG96TFhyQmRKdG1sbi9KNXZvQkl3OElJNjRJY2RJOHFaalpnRForS3MwUTZvVWRpYUt3SXk3WDRvVEcyR2k0ZHBhREZDeU4iLCJtYWMiOiJkMWQwNzE3NzBlOGI0Mzk0MDQxNGM4ZWVlYzE1NzE5OWQ1ZWQ4OTJhNThiODA1MWI4M2I2NDE5OWYwZDBiZTE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,id;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">https://dev.psp.com/internal/invoices?activeTab=3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">dev.psp.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1329192262\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1913373605 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">4|q5hZpWB7QSHiPq0hTFEb4e5IyIUPCzdE5GMeANTAcy4FtWS6lj04ZIiQL38G|</span>\"\n  \"<span class=sf-dump-key>remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">5|bQicjYEg6cF5oW38bBsKPo6sbK55hIXGXRCTgCFFQxCRgmArAxSeD8RHgEnv|$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>ceval_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TBncuK1Z7kHcGjnRHlb51hN4z6hEodSBkwEbeDjj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1913373605\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-74219502 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 08:27:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6ImtFOExQOUF2dlU2bnp0dVJaNHNicGc9PSIsInZhbHVlIjoiZG51YU4xVWg3d1cyL012dTBhODlKeDJhQ3BpdzJPTGdGTVgrb3R0cEE1V0FLN25jR0psWHc5VFBzWGVBV1l1ckgzYlJkVWQxYzJGdm4wOU1EUmtCYW9XbEZkclY2WkgyWVY0bW4rMExMNE1OMDNQdHdaMFBQN1IyNHlXald1MGciLCJtYWMiOiI3ODY5ZDNhYWQ5NTRjYjE0ZWE3NGQ1NjhmMjVmNmZlZGFlODA0ZTkyNTdlOWViOTI5YWU4NTFkNzllMTA0NTAwIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 10:27:08 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"453 characters\">ceval_app_session=eyJpdiI6IlFOODRTSk1XTHZXbElreWYrY0Nwa2c9PSIsInZhbHVlIjoibzFubEl4b25maUdpTXBDdGkrREwvU0phY1lzTHFHY0xoa2dMdU9DY05Tc25KYUR3ZjNDRitQZ1ZiOUduTnM0YlVEOEhXVzZuQ3B3czkrR3Exc0JZT0o1Znl3b2xSWVYrMjQ1K2hVUjRzRVV4WjNrNkRjK3hUWDBuRm4vNjR1Q3QiLCJtYWMiOiJhMWZiNDMyODk0NjFjMGRjNjI2NDJlZTkxMDYxNGMwMGE3MWZhNmZlMjc0NGM5ZTAyM2I1ODBlYzllZTVlMmIzIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 10:27:08 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6ImtFOExQOUF2dlU2bnp0dVJaNHNicGc9PSIsInZhbHVlIjoiZG51YU4xVWg3d1cyL012dTBhODlKeDJhQ3BpdzJPTGdGTVgrb3R0cEE1V0FLN25jR0psWHc5VFBzWGVBV1l1ckgzYlJkVWQxYzJGdm4wOU1EUmtCYW9XbEZkclY2WkgyWVY0bW4rMExMNE1OMDNQdHdaMFBQN1IyNHlXald1MGciLCJtYWMiOiI3ODY5ZDNhYWQ5NTRjYjE0ZWE3NGQ1NjhmMjVmNmZlZGFlODA0ZTkyNTdlOWViOTI5YWU4NTFkNzllMTA0NTAwIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 10:27:08 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"425 characters\">ceval_app_session=eyJpdiI6IlFOODRTSk1XTHZXbElreWYrY0Nwa2c9PSIsInZhbHVlIjoibzFubEl4b25maUdpTXBDdGkrREwvU0phY1lzTHFHY0xoa2dMdU9DY05Tc25KYUR3ZjNDRitQZ1ZiOUduTnM0YlVEOEhXVzZuQ3B3czkrR3Exc0JZT0o1Znl3b2xSWVYrMjQ1K2hVUjRzRVV4WjNrNkRjK3hUWDBuRm4vNjR1Q3QiLCJtYWMiOiJhMWZiNDMyODk0NjFjMGRjNjI2NDJlZTkxMDYxNGMwMGE3MWZhNmZlMjc0NGM5ZTAyM2I1ODBlYzllZTVlMmIzIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 10:27:08 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74219502\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-762611405 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>password_hash_internal</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>ListOrders_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">https://dev.psp.com/internal/invoices?activeTab=3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>ListSubscriptions_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-28</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-12-31</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762611405\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://dev.psp.com/internal/invoices?activeTab=3", "action_name": "filament.internal.resources.invoices.index", "controller_action": "App\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices"}, "badge": null}}