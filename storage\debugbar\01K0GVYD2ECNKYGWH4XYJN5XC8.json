{"__meta": {"id": "01K0GVYD2ECNKYGWH4XYJN5XC8", "datetime": "2025-07-19 15:13:55", "utime": **********.662495, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752912834.755002, "end": **********.66252, "duration": 0.9075179100036621, "duration_str": "908ms", "measures": [{"label": "Booting", "start": 1752912834.755002, "relative_start": 0, "end": **********.095419, "relative_end": **********.095419, "duration": 0.*****************, "duration_str": "340ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.09544, "relative_start": 0.****************, "end": **********.662523, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "567ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.605424, "relative_start": 0.****************, "end": **********.605961, "relative_end": **********.605961, "duration": 0.0005371570587158203, "duration_str": "537μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.661676, "relative_start": 0.****************, "end": **********.661959, "relative_end": **********.661959, "duration": 0.0002830028533935547, "duration_str": "283μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8404712, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.18", "Environment": "local", "Debug Mode": "Enabled", "URL": "dev.psp.com", "Timezone": "Asia/Jakarta", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.019330000000000003, "accumulated_duration_str": "19.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 5 and `user_type` = 'admin' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.612752, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "psp_dev", "explain": null, "start_percent": 0, "width_percent": 18.314}, {"sql": "select * from `plans` where `is_active` = 1 and `plans`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 74}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.626083, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "GrowthChart.php:74", "source": {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FWidgets%2FGrowthChart.php&line=74", "ajax": false, "filename": "GrowthChart.php", "line": "74"}, "connection": "psp_dev", "explain": null, "start_percent": 18.314, "width_percent": 11.33}, {"sql": "select\ndate_format(start_date, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 1 and `subscriptions`.`deleted_at` is null and `start_date` between '2025-01-28 00:00:00' and '2025-12-31 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 1, "2025-01-28 00:00:00", "2025-12-31 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.630083, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 29.643, "width_percent": 16.451}, {"sql": "select\ndate_format(start_date, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 2 and `subscriptions`.`deleted_at` is null and `start_date` between '2025-01-28 00:00:00' and '2025-12-31 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 2, "2025-01-28 00:00:00", "2025-12-31 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.635756, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 46.094, "width_percent": 14.537}, {"sql": "select\ndate_format(start_date, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 3 and `subscriptions`.`deleted_at` is null and `start_date` between '2025-01-28 00:00:00' and '2025-12-31 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 3, "2025-01-28 00:00:00", "2025-12-31 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.641673, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 60.631, "width_percent": 13.554}, {"sql": "select\ndate_format(start_date, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 4 and `subscriptions`.`deleted_at` is null and `start_date` between '2025-01-28 00:00:00' and '2025-12-31 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 4, "2025-01-28 00:00:00", "2025-12-31 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.6459699, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 74.185, "width_percent": 13.088}, {"sql": "select\ndate_format(start_date, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 7 and `subscriptions`.`deleted_at` is null and `start_date` between '2025-01-28 00:00:00' and '2025-12-31 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 7, "2025-01-28 00:00:00", "2025-12-31 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.651246, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 87.274, "width_percent": 12.726}]}, "models": {"data": {"App\\Models\\Plan": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}, "App\\Models\\UserInternal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserInternal.php&line=1", "ajax": false, "filename": "UserInternal.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": {"app.filament.internal.resources.subscription-resource.widgets.growth-chart #SCVte2AaAJykyE2xah3t": "array:4 [\n  \"data\" => array:3 [\n    \"dataChecksum\" => \"944bc973626d6ff7867334d050a2c100\"\n    \"filter\" => null\n    \"filters\" => array:2 [\n      \"startDate\" => \"2025-01-28\"\n      \"endDate\" => \"2025-12-31\"\n    ]\n  ]\n  \"name\" => \"app.filament.internal.resources.subscription-resource.widgets.growth-chart\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart\"\n  \"id\" => \"SCVte2AaAJykyE2xah3t\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://dev.psp.com/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "telescope": "<a href=\"https://dev.psp.com/_debugbar/telescope/9f6d2eb0-709c-4597-8748-f54d6534f5dc\" target=\"_blank\">View in Telescope</a>", "duration": "909ms", "peak_memory": "12MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2059832451 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2059832451\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"489 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:null,&quot;filter&quot;:null,&quot;filters&quot;:[{&quot;startDate&quot;:&quot;2025-01-28&quot;,&quot;endDate&quot;:&quot;2025-12-31&quot;},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;SCVte2AaAJykyE2xah3t&quot;,&quot;name&quot;:&quot;app.filament.internal.resources.subscription-resource.widgets.growth-chart&quot;,&quot;path&quot;:&quot;internal\\/subscriptions&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;props&quot;:[&quot;filters&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;a4a42eff86f2bfa4950dca5a18c4d20b1d5f638cdc8e516ea2ee32e18489d8ca&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"344 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJmaWx0ZXJzIjpbeyJzdGFydERhdGUiOiIyMDI1LTAxLTI4IiwiZW5kRGF0ZSI6IjIwMjUtMTItMzEifSx7InMiOiJhcnIifV19LHsicyI6ImFyciJ9XX0sIm1lbW8iOnsiaWQiOiJxUXRDVDY1WjVTdlFNM3Z5Z1l0dyIsIm5hbWUiOiJfX21vdW50UGFyYW1zQ29udGFpbmVyIn0sImNoZWNrc3VtIjoiMDk0ZDM3NzRkN2U0N2Y3ZWFhNTVkOTJlY2QyNDVkODBlNmUyZDA4YjQ4YWMyZWY2YjNjY2M2NTkxYmQ2YjZmNiJ9</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1448674697 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1695 characters\">remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5QMVd4eVAzYWVDZTJPbU1IOC9lVUE9PSIsInZhbHVlIjoiOXJ2dHFYRUd1WU1TRis0Njg2VHRKV3FlTUhPR3ZOZ0FMSzVTdG9SK2RWTCsvOEViMEFCbTdBdHRTc1h4WXZSb0lvU2x2MTBFODBCaFREVlBScW9zSkhjR2xURktSMjFaUW9sYUNFVXBkUkRLQmdrVkZtQzJIOXBFVlpCVnNTY1BvNWs0YWtwMGxKS3VNdTIvQldFZWdnPT0iLCJtYWMiOiI3ODdkN2ExYmExZjU5MzE2ODBmZTU4NmRhNmQxMDVjNTUxMWE3ODEyY2EzYWVkNTU0NWUwOTk4ZmI2YjczNDdjIiwidGFnIjoiIn0%3D; remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IitDNnNWWlM0T0JuVitIL2ptLzY3WWc9PSIsInZhbHVlIjoiYkRZRFJ5MlMxbFBXL2dqNzlzQmwvQW9HVk11N3hYS3IxMHZZMkZCWWhjWko3N1kvbDYvTGU2UDZ0NE1vbHRBUUwrdUFSam15akJlVjdvbVBGWXFLVFUxVVlBWUM2UkxNVmgyWGVZU2NnL21RMjJybUZuZzFOTFN1Z3RVRUNobCtiNjJmOEs4TWZ0NGM1bittb3ZaLzNFZFVvTTRycVM5RHBLaGsyUThNVzZqdFNiYnBldVV0TWNZLzRocjcxUGZ0UUhBaG5RWXlIdEU0d3RnQUZMZVAwTldqMHJyYVBlZ0lrQlphOHNuWVpBQT0iLCJtYWMiOiI1ZjhkMDMzYzRiMzAyOWFmZWJhNThmYzYwODU3Yjc2N2NkYTg1OWJmZTkxNzUwYWVhMjg0ODQzODMxNDlkOTg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Iis2V0w4dGFTbzNsKzhORFRMdkIzcWc9PSIsInZhbHVlIjoicTRhYjN6N05aeVVPV3RRWktndUtOc3oyNkdnTldzdmMxY3hvS1c3WEFNNzQxR2ZRY0xHTUVJTEpEOHRSTDhPRU5NU2VJemRRbHRUeDNiWlVGcDBtMTJqNVVXT1dCNG9MeStaancxalUwSFA5WDlleHNONVlydTN0RWlOTUlBb2UiLCJtYWMiOiIzNzllMzgwYjA0ZjI3OTRmZjEyMzgyYjZhZGRkMTRiODk5NTIyNjNmOTEwNjEzNGM4YmJhNzFjMjM4MTJlMGMwIiwidGFnIjoiIn0%3D; ceval_app_session=eyJpdiI6ImNpa1dZaThIdkhvb01tc3BkM0c4MGc9PSIsInZhbHVlIjoibEFIanl5VU1sZE9lblY2V0FzbXN4NWJYMUltYU1JTG1uNTZOdG1FQXJTOG5SN1VKd1czRVFuUGVJT0l1UytSM203TERRbWJidmZ4NXFHbkN4MU9US2ttZnRkSFNhSVhpbzFsZ0g1d3ZFTWxIaSsxVkgzWTN4ZTJlNUVtdnRQWXgiLCJtYWMiOiI2ODNkNGIxYzE0YjA0MmY5ZGZiNDFkNTEyODZjM2NmMDE4OWVhNzczMzBjNWE4NzMxMWU1ZjM5MDM2MjQ4NzJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,id;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"100 characters\">https://dev.psp.com/internal/subscriptions?filters[startDate]=2025-01-28&amp;filters[endDate]=2025-12-31</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://dev.psp.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1051</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">dev.psp.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448674697\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-649309551 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">4|q5hZpWB7QSHiPq0hTFEb4e5IyIUPCzdE5GMeANTAcy4FtWS6lj04ZIiQL38G|</span>\"\n  \"<span class=sf-dump-key>remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">5|bQicjYEg6cF5oW38bBsKPo6sbK55hIXGXRCTgCFFQxCRgmArAxSeD8RHgEnv|$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>ceval_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TBncuK1Z7kHcGjnRHlb51hN4z6hEodSBkwEbeDjj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-649309551\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2028533142 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 08:13:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028533142\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1602914596 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>password_hash_internal</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>ListOrders_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"108 characters\">https://dev.psp.com/internal/subscriptions?filters%5BstartDate%5D=2025-01-28&amp;filters%5BendDate%5D=2025-12-31</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>ListSubscriptions_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-28</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-12-31</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602914596\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://dev.psp.com/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}