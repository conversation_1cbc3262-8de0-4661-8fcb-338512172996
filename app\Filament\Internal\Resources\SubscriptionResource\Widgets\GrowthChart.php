<?php

namespace App\Filament\Internal\Resources\SubscriptionResource\Widgets;

use Carbon\Carbon;
use App\Models\Plan;
use Flowframe\Trend\Trend;
use App\Models\Subscription;
use Flowframe\Trend\TrendValue;
use Filament\Widgets\ChartWidget;
use Filament\Widgets\Concerns\InteractsWithPageFilters;

class GrowthChart extends ChartWidget
{
    use InteractsWithPageFilters;

    protected static ?string $heading = 'Growth Chart';

    protected static ?string $description = 'Growth of subscriptions over time.';
    protected static ?string $pollingInterval = null;

    protected static ?string $maxHeight = '300px';

    /**
     * Get the data for the chart.
     *
     * @return array
     */
    public function getColumnSpan(): int | string | array
    {
        return 'full';
    }

    /**
     * Get the data for the chart.
     *
     * @return array
     */
    protected function getData(): array
    {
        $startDate = $this->filters['startDate'] ?? Carbon::now()->subDays(30);
        $endDate = $this->filters['endDate'] ?? Carbon::now();

        $data = $this->getTrendData($startDate, $endDate);

        return [
            'datasets' => $data,
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        ];
    }

    /**
     * Get the type of chart to be displayed.
     *
     * @return string
     */
    protected function getType(): string
    {
        return 'line';
    }

    /**
     * Get the trend data for the chart.
     *
     * @param  string  $startDate
     * @param  string  $endDate
     * @return array
     */
    private function getTrendData($startDate, $endDate): array
    {
        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        $plan = Plan::where('is_active', true)->get();
        $data = [];

        foreach ($plan as $key => $value) {
            $query = Subscription::where('payment_status', 'paid')
                ->where('plan_id', $value->id);

            $trend = Trend::query($query)
                ->dateColumn('start_date')
                ->between(
                    start: $startDate,
                    end: $endDate
                )
                ->perMonth()
                ->count();

            $data[$key] = [
                'label' => $value->name,
                'data' => $trend->map(fn(TrendValue $value) => $value->aggregate),
                'borderColor' => $this->_randomColor(),

            ];
        }

        return $data;
    }

    /**
     * Generate a random color for the chart.
     *
     * @return string
     */
    private function _randomColor(): string
    {
        return sprintf('#%06X', mt_rand(0, 0xFFFFFF));
    }
}
