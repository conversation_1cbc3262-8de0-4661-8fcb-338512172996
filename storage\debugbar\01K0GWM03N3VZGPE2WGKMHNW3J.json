{"__meta": {"id": "01K0GWM03N3VZGPE2WGKMHNW3J", "datetime": "2025-07-19 15:25:43", "utime": **********.286006, "method": "GET", "uri": "/internal/invoices/882c6ab9-9938-4766-ac39-cbd8b6de1304", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.096772, "end": **********.286021, "duration": 1.189249038696289, "duration_str": "1.19s", "measures": [{"label": "Booting", "start": **********.096772, "relative_start": 0, "end": **********.424551, "relative_end": **********.424551, "duration": 0.*****************, "duration_str": "328ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.424572, "relative_start": 0.*****************, "end": **********.286023, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "861ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.823272, "relative_start": 0.****************, "end": **********.824187, "relative_end": **********.824187, "duration": 0.0009150505065917969, "duration_str": "915μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.internal.resources.invoice-resource.pages.view-invoice", "start": **********.911263, "relative_start": 0.****************, "end": **********.911263, "relative_end": **********.911263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.929879, "relative_start": 0.****************, "end": **********.929879, "relative_end": **********.929879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.005343, "relative_start": 0.9085710048675537, "end": **********.005343, "relative_end": **********.005343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.029187, "relative_start": 0.9324150085449219, "end": **********.029187, "relative_end": **********.029187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.036628, "relative_start": 0.9398560523986816, "end": **********.036628, "relative_end": **********.036628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.072223, "relative_start": 0.9754509925842285, "end": **********.072223, "relative_end": **********.072223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e93d402ed1f3da8a07f4840136a03cb", "start": **********.140543, "relative_start": 1.0437710285186768, "end": **********.140543, "relative_end": **********.140543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.283743, "relative_start": 1.1869709491729736, "end": **********.283828, "relative_end": **********.283828, "duration": 8.511543273925781e-05, "duration_str": "85μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.285406, "relative_start": 1.1886341571807861, "end": **********.285545, "relative_end": **********.285545, "duration": 0.00013899803161621094, "duration_str": "139μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 9432040, "peak_usage_str": "9MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.18", "Environment": "local", "Debug Mode": "Enabled", "URL": "dev.psp.com", "Timezone": "Asia/Jakarta", "Locale": "en"}}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "filament.internal.resources.invoice-resource.pages.view-invoice", "param_count": null, "params": [], "start": **********.911219, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\resources\\views/filament/internal/resources/invoice-resource/pages/view-invoice.blade.phpfilament.internal.resources.invoice-resource.pages.view-invoice", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fresources%2Fviews%2Ffilament%2Finternal%2Fresources%2Finvoice-resource%2Fpages%2Fview-invoice.blade.php&line=1", "ajax": false, "filename": "view-invoice.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.929841, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.005306, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.029163, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "livewire::tailwind", "param_count": null, "params": [], "start": **********.03656, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.072181, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::5e93d402ed1f3da8a07f4840136a03cb", "param_count": null, "params": [], "start": **********.140503, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/5e93d402ed1f3da8a07f4840136a03cb.blade.php__components::5e93d402ed1f3da8a07f4840136a03cb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F5e93d402ed1f3da8a07f4840136a03cb.blade.php&line=1", "ajax": false, "filename": "5e93d402ed1f3da8a07f4840136a03cb.blade.php", "line": "?"}}]}, "queries": {"count": 18, "nb_statements": 18, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.049010000000000005, "accumulated_duration_str": "49.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 5 and `user_type` = 'admin' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8287818, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "psp_dev", "explain": null, "start_percent": 0, "width_percent": 5.468}, {"sql": "select * from `invoices` where `uuid` = '882c6ab9-9938-4766-ac39-cbd8b6de1304' and `invoices`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["882c6ab9-9938-4766-ac39-cbd8b6de1304"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Pages\\Concerns\\InteractsWithRecord.php", "line": 23}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ViewRecord.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Pages\\ViewRecord.php", "line": 59}, {"index": 19, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ViewInvoice.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.8434858, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "Resource.php:192", "source": {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FResource.php&line=192", "ajax": false, "filename": "Resource.php", "line": "192"}, "connection": "psp_dev", "explain": null, "start_percent": 5.468, "width_percent": 5.162}, {"sql": "select * from `settings` where `key` = 'company_name' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["company_name"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ViewInvoice.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.862796, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "ViewInvoice.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ViewInvoice.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FViewInvoice.php&line=44", "ajax": false, "filename": "ViewInvoice.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 10.63, "width_percent": 4.917}, {"sql": "select * from `settings` where `key` = 'company_email' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["company_email"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ViewInvoice.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.8679569, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "ViewInvoice.php:45", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ViewInvoice.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FViewInvoice.php&line=45", "ajax": false, "filename": "ViewInvoice.php", "line": "45"}, "connection": "psp_dev", "explain": null, "start_percent": 15.548, "width_percent": 5.101}, {"sql": "select * from `settings` where `key` = 'company_address' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["company_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ViewInvoice.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.8721728, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "ViewInvoice.php:46", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ViewInvoice.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FViewInvoice.php&line=46", "ajax": false, "filename": "ViewInvoice.php", "line": "46"}, "connection": "psp_dev", "explain": null, "start_percent": 20.649, "width_percent": 4.428}, {"sql": "select * from `settings` where `key` = 'company_phone' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["company_phone"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ViewInvoice.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.876335, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "ViewInvoice.php:47", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ViewInvoice.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FViewInvoice.php&line=47", "ajax": false, "filename": "ViewInvoice.php", "line": "47"}, "connection": "psp_dev", "explain": null, "start_percent": 25.077, "width_percent": 5.346}, {"sql": "select * from `payments` where `payments`.`invoice_id` = 140 and `payments`.`invoice_id` is not null and `payments`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": [140], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ViewInvoice.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.883318, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "ViewInvoice.php:58", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ViewInvoice.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FViewInvoice.php&line=58", "ajax": false, "filename": "ViewInvoice.php", "line": "58"}, "connection": "psp_dev", "explain": null, "start_percent": 30.422, "width_percent": 5.529}, {"sql": "select * from `invoice_billing_details` where `invoice_billing_details`.`invoice_id` = 140 and `invoice_billing_details`.`invoice_id` is not null limit 1", "type": "query", "params": [], "bindings": [140], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.internal.resources.invoice-resource.pages.view-invoice", "file": "D:\\WebData\\www\\psp\\resources\\views/filament/internal/resources/invoice-resource/pages/view-invoice.blade.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9141588, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "filament.internal.resources.invoice-resource.pages.view-invoice:57", "source": {"index": 21, "namespace": "view", "name": "filament.internal.resources.invoice-resource.pages.view-invoice", "file": "D:\\WebData\\www\\psp\\resources\\views/filament/internal/resources/invoice-resource/pages/view-invoice.blade.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fresources%2Fviews%2Ffilament%2Finternal%2Fresources%2Finvoice-resource%2Fpages%2Fview-invoice.blade.php&line=57", "ajax": false, "filename": "view-invoice.blade.php", "line": "57"}, "connection": "psp_dev", "explain": null, "start_percent": 35.952, "width_percent": 6.57}, {"sql": "select * from `invoice_items` where `invoice_items`.`invoice_id` = 140 and `invoice_items`.`invoice_id` is not null and `invoice_items`.`deleted_at` is null", "type": "query", "params": [], "bindings": [140], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "filament.internal.resources.invoice-resource.pages.view-invoice", "file": "D:\\WebData\\www\\psp\\resources\\views/filament/internal/resources/invoice-resource/pages/view-invoice.blade.php", "line": 106}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.9349658, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "filament.internal.resources.invoice-resource.pages.view-invoice:106", "source": {"index": 20, "namespace": "view", "name": "filament.internal.resources.invoice-resource.pages.view-invoice", "file": "D:\\WebData\\www\\psp\\resources\\views/filament/internal/resources/invoice-resource/pages/view-invoice.blade.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fresources%2Fviews%2Ffilament%2Finternal%2Fresources%2Finvoice-resource%2Fpages%2Fview-invoice.blade.php&line=106", "ajax": false, "filename": "view-invoice.blade.php", "line": "106"}, "connection": "psp_dev", "explain": null, "start_percent": 42.522, "width_percent": 6.57}, {"sql": "select * from `users` where `users`.`id` = 4 and `user_type` = 'user' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4, "user"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.internal.resources.invoice-resource.pages.view-invoice", "file": "D:\\WebData\\www\\psp\\resources\\views/filament/internal/resources/invoice-resource/pages/view-invoice.blade.php", "line": 240}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.94531, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "filament.internal.resources.invoice-resource.pages.view-invoice:240", "source": {"index": 21, "namespace": "view", "name": "filament.internal.resources.invoice-resource.pages.view-invoice", "file": "D:\\WebData\\www\\psp\\resources\\views/filament/internal/resources/invoice-resource/pages/view-invoice.blade.php", "line": 240}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fresources%2Fviews%2Ffilament%2Finternal%2Fresources%2Finvoice-resource%2Fpages%2Fview-invoice.blade.php&line=240", "ajax": false, "filename": "view-invoice.blade.php", "line": "240"}, "connection": "psp_dev", "explain": null, "start_percent": 49.092, "width_percent": 4.938}, {"sql": "select count(*) as aggregate from `payments` where `invoice_id` = 140 and `payments`.`deleted_at` is null", "type": "query", "params": [], "bindings": [140], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.964571, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "psp_dev", "explain": null, "start_percent": 54.03, "width_percent": 5.693}, {"sql": "select * from `payments` where `invoice_id` = 140 and `payments`.`deleted_at` is null order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [140], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.971144, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 59.723, "width_percent": 5.162}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (5) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\UserInternal'", "type": "query", "params": [], "bindings": ["App\\Models\\UserInternal"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.080317, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "psp_dev", "explain": null, "start_percent": 64.885, "width_percent": 5.591}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (5) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\UserInternal'", "type": "query", "params": [], "bindings": ["App\\Models\\UserInternal"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.0872889, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "psp_dev", "explain": null, "start_percent": 70.475, "width_percent": 4.02}, {"sql": "select count(*) as aggregate from `tickets` where `status` = 'open' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.113806, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "TicketResource.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FTicketResource.php&line=33", "ajax": false, "filename": "TicketResource.php", "line": "33"}, "connection": "psp_dev", "explain": null, "start_percent": 74.495, "width_percent": 3.163}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.125191, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "psp_dev", "explain": null, "start_percent": 77.658, "width_percent": 7.774}, {"sql": "select count(*) as aggregate from `tickets` where `status` = 'open' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.193665, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "TicketResource.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FTicketResource.php&line=33", "ajax": false, "filename": "TicketResource.php", "line": "33"}, "connection": "psp_dev", "explain": null, "start_percent": 85.432, "width_percent": 7.713}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.205993, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "psp_dev", "explain": null, "start_percent": 93.144, "width_percent": 6.856}]}, "models": {"data": {"App\\Models\\Setting": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\Payment": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\UserInternal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserInternal.php&line=1", "ajax": false, "filename": "UserInternal.php", "line": "?"}}, "App\\Models\\Invoice": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "App\\Models\\InvoiceBillingDetail": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FInvoiceBillingDetail.php&line=1", "ajax": false, "filename": "InvoiceBillingDetail.php", "line": "?"}}, "App\\Models\\InvoiceItem": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FInvoiceItem.php&line=1", "ajax": false, "filename": "InvoiceItem.php", "line": "?"}}, "App\\Models\\UserMember": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserMember.php&line=1", "ajax": false, "filename": "UserMember.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 14, "is_counter": true}, "livewire": {"data": {"app.filament.internal.resources.invoice-resource.pages.view-invoice #Hq6PaeOOWasqqsZ75Fia": "array:4 [\n  \"data\" => array:47 [\n    \"companyName\" => \"My Company\"\n    \"companyEmail\" => \"My Company Email\"\n    \"companyAddress\" => \"My Company Address\"\n    \"companyPhone\" => \"My Company Phone\"\n    \"companyVatNumber\" => null\n    \"paymentHistories\" => array:2 [\n      0 => array:17 [\n        \"id\" => 78\n        \"uuid\" => \"6d7bbc88-7b49-47ea-ac3c-4df227e82322\"\n        \"user_id\" => 4\n        \"invoice_id\" => 140\n        \"transaction_id\" => \"92919695-bf27-400a-bb91-ceef0d4ae42c\"\n        \"amount\" => \"333000.00\"\n        \"payment_date\" => \"2025-07-15\"\n        \"status\" => \"paid\"\n        \"payment_method\" => \"echannel\"\n        \"payment_provider\" => \"midtrans\"\n        \"created_by\" => null\n        \"updated_by\" => null\n        \"deleted_by\" => null\n        \"deleted_at\" => null\n        \"created_at\" => \"2025-07-15T14:54:49.000000Z\"\n        \"updated_at\" => \"2025-07-15T14:54:49.000000Z\"\n        \"payment_callback\" => array:16 [\n          \"transaction_time\" => \"2025-07-15 21:45:04\"\n          \"transaction_status\" => \"settlement\"\n          \"transaction_id\" => \"92919695-bf27-400a-bb91-ceef0d4ae42c\"\n          \"status_message\" => \"midtrans payment notification\"\n          \"status_code\" => \"200\"\n          \"signature_key\" => \"0e3f09cc3d8a7334d3799ef4c8a66dbbde397531f3a4f970192751d498938f02a63d91abe6e793f140bca08e95a65c2bb32d171843382f71693447ee2d1082c7\"\n          \"settlement_time\" => \"2025-07-15 21:54:47\"\n          \"payment_type\" => \"echannel\"\n          \"order_id\" => \"HAR-AFUNG\"\n          \"merchant_id\" => \"G600891541\"\n          \"gross_amount\" => \"333000.00\"\n          \"fraud_status\" => \"accept\"\n          \"expiry_time\" => \"2025-07-16 21:45:04\"\n          \"currency\" => \"IDR\"\n          \"biller_code\" => \"70012\"\n          \"bill_key\" => \"************\"\n        ]\n      ]\n      1 => array:17 [\n        \"id\" => 77\n        \"uuid\" => \"10050986-14f2-4807-a283-b799fbaaa42b\"\n        \"user_id\" => 4\n        \"invoice_id\" => 140\n        \"transaction_id\" => \"92919695-bf27-400a-bb91-ceef0d4ae42c\"\n        \"amount\" => \"333000.00\"\n        \"payment_date\" => \"2025-07-15\"\n        \"status\" => \"pending\"\n        \"payment_method\" => \"echannel\"\n        \"payment_provider\" => \"midtrans\"\n        \"created_by\" => null\n        \"updated_by\" => null\n        \"deleted_by\" => null\n        \"deleted_at\" => null\n        \"created_at\" => \"2025-07-15T14:45:06.000000Z\"\n        \"updated_at\" => \"2025-07-15T14:45:06.000000Z\"\n        \"payment_callback\" => array:15 [\n          \"transaction_time\" => \"2025-07-15 21:45:04\"\n          \"transaction_status\" => \"pending\"\n          \"transaction_id\" => \"92919695-bf27-400a-bb91-ceef0d4ae42c\"\n          \"status_message\" => \"midtrans payment notification\"\n          \"status_code\" => \"201\"\n          \"signature_key\" => \"d8f7c95e2180f4665a93d8b9f8f0e683cb91f1ddd9d533885dcefad96c8d37e2ab6d5de933d5ae7152379adf09ee8e7398d0c2f2adcab4a67794e6e8ca566ba8\"\n          \"payment_type\" => \"echannel\"\n          \"order_id\" => \"HAR-AFUNG\"\n          \"merchant_id\" => \"G600891541\"\n          \"gross_amount\" => \"333000.00\"\n          \"fraud_status\" => \"accept\"\n          \"expiry_time\" => \"2025-07-16 21:45:04\"\n          \"currency\" => \"IDR\"\n          \"biller_code\" => \"70012\"\n          \"bill_key\" => \"************\"\n        ]\n      ]\n    ]\n    \"paymentStatusColor\" => \"green\"\n    \"data\" => array:23 [\n      \"id\" => 140\n      \"uuid\" => \"882c6ab9-9938-4766-ac39-cbd8b6de1304\"\n      \"user_id\" => 4\n      \"subscription_id\" => 83\n      \"order_id\" => null\n      \"coupon_id\" => null\n      \"invoice_date\" => \"2025-07-15 21:44:24\"\n      \"tax_amount\" => \"33000.00\"\n      \"tax_description\" => \"PPN 11%\"\n      \"discount_amount\" => \"0.00\"\n      \"discount_description\" => \"\"\n      \"due_date\" => \"2025-07-16 21:44:24\"\n      \"invoice_number\" => \"HAR-AFUNG\"\n      \"total_amount\" => \"333000.00\"\n      \"payment_status\" => \"paid\"\n      \"payment_date\" => \"2025-07-15 21:54:49\"\n      \"created_by\" => 4\n      \"updated_by\" => 4\n      \"deleted_by\" => null\n      \"deleted_at\" => null\n      \"created_at\" => \"2025-07-15T14:44:24.000000Z\"\n      \"updated_at\" => \"2025-07-15T14:54:49.000000Z\"\n      \"payment_url\" => \"https://app.sandbox.midtrans.com/snap/v4/redirection/738db5ca-dc91-456f-9f64-92ab19c59032\"\n    ]\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\Invoice {#3228\n      #connection: \"mysql\"\n      #table: \"invoices\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:23 [\n        \"id\" => 140\n        \"uuid\" => \"882c6ab9-9938-4766-ac39-cbd8b6de1304\"\n        \"user_id\" => 4\n        \"subscription_id\" => 83\n        \"order_id\" => null\n        \"coupon_id\" => null\n        \"invoice_date\" => \"2025-07-15 21:44:24\"\n        \"tax_amount\" => \"33000.00\"\n        \"tax_description\" => \"PPN 11%\"\n        \"discount_amount\" => \"0.00\"\n        \"discount_description\" => \"\"\n        \"due_date\" => \"2025-07-16 21:44:24\"\n        \"invoice_number\" => \"HAR-AFUNG\"\n        \"total_amount\" => \"333000.00\"\n        \"payment_status\" => \"paid\"\n        \"payment_date\" => \"2025-07-15 21:54:49\"\n        \"created_by\" => 4\n        \"updated_by\" => 4\n        \"deleted_by\" => null\n        \"deleted_at\" => null\n        \"created_at\" => \"2025-07-15 21:44:24\"\n        \"updated_at\" => \"2025-07-15 21:54:49\"\n        \"payment_url\" => \"https://app.sandbox.midtrans.com/snap/v4/redirection/738db5ca-dc91-456f-9f64-92ab19c59032\"\n      ]\n      #original: array:23 [\n        \"id\" => 140\n        \"uuid\" => \"882c6ab9-9938-4766-ac39-cbd8b6de1304\"\n        \"user_id\" => 4\n        \"subscription_id\" => 83\n        \"order_id\" => null\n        \"coupon_id\" => null\n        \"invoice_date\" => \"2025-07-15 21:44:24\"\n        \"tax_amount\" => \"33000.00\"\n        \"tax_description\" => \"PPN 11%\"\n        \"discount_amount\" => \"0.00\"\n        \"discount_description\" => \"\"\n        \"due_date\" => \"2025-07-16 21:44:24\"\n        \"invoice_number\" => \"HAR-AFUNG\"\n        \"total_amount\" => \"333000.00\"\n        \"payment_status\" => \"paid\"\n        \"payment_date\" => \"2025-07-15 21:54:49\"\n        \"created_by\" => 4\n        \"updated_by\" => 4\n        \"deleted_by\" => null\n        \"deleted_at\" => null\n        \"created_at\" => \"2025-07-15 21:44:24\"\n        \"updated_at\" => \"2025-07-15 21:54:49\"\n        \"payment_url\" => \"https://app.sandbox.midtrans.com/snap/v4/redirection/738db5ca-dc91-456f-9f64-92ab19c59032\"\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:19 [\n        0 => \"uuid\"\n        1 => \"user_id\"\n        2 => \"subscription_id\"\n        3 => \"order_id\"\n        4 => \"coupon_id\"\n        5 => \"invoice_date\"\n        6 => \"due_date\"\n        7 => \"invoice_number\"\n        8 => \"total_amount\"\n        9 => \"payment_status\"\n        10 => \"payment_date\"\n        11 => \"discount_amount\"\n        12 => \"discount_description\"\n        13 => \"tax_amount\"\n        14 => \"tax_description\"\n        15 => \"payment_url\"\n        16 => \"created_by\"\n        17 => \"updated_by\"\n        18 => \"deleted_by\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"isTableLoaded\" => false\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableRecordsPerPage\" => 10\n    \"isTableReordering\" => false\n    \"tableColumnSearches\" => []\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableFilters\" => null\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.internal.resources.invoice-resource.pages.view-invoice\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice\"\n  \"id\" => \"Hq6PaeOOWasqqsZ75Fia\"\n]", "filament.livewire.notifications #nA9PaXyMgUOxqM3mYmgA": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#4855\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"nA9PaXyMgUOxqM3mYmgA\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 20, "messages": [{"message": "[\n  ability => page_Product,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1857443432 data-indent-pad=\"  \"><span class=sf-dump-note>page_Product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">page_Product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857443432\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.092503, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1149940694 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1149940694\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.100216, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-551082611 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-551082611\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101376, "xdebug_link": null}, {"message": "[\n  ability => view_any_post::category,\n  target => null,\n  result => null,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2137357344 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post::category </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_post::category</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137357344\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.103196, "xdebug_link": null}, {"message": "[\n  ability => view_any_post,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1219825614 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_post</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219825614\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.10386, "xdebug_link": null}, {"message": "[\n  ability => view_any_regulation,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1423563333 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_regulation </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">view_any_regulation</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1423563333\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.107249, "xdebug_link": null}, {"message": "[\n  ability => view_any_setting,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-623851982 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623851982\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.108663, "xdebug_link": null}, {"message": "[\n  ability => view_any_userinternal,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1280182114 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_userinternal </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_userinternal</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280182114\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.118983, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-387569392 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387569392\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.12308, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user_internal => 5,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-7297369 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7297369\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.123614, "xdebug_link": null}, {"message": "[\n  ability => page_Product,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-490004468 data-indent-pad=\"  \"><span class=sf-dump-note>page_Product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">page_Product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-490004468\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.175249, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-483367883 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483367883\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.182308, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-843947816 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-843947816\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.183421, "xdebug_link": null}, {"message": "[\n  ability => view_any_post::category,\n  target => null,\n  result => null,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-270457584 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post::category </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_post::category</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-270457584\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.186019, "xdebug_link": null}, {"message": "[\n  ability => view_any_post,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-505895533 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_post</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505895533\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.186792, "xdebug_link": null}, {"message": "[\n  ability => view_any_regulation,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-936859227 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_regulation </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">view_any_regulation</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-936859227\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.188477, "xdebug_link": null}, {"message": "[\n  ability => view_any_setting,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-424589908 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-424589908\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.189805, "xdebug_link": null}, {"message": "[\n  ability => view_any_userinternal,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-735128977 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_userinternal </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_userinternal</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-735128977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.200534, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1852964425 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852964425\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.204166, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user_internal => 5,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2078437258 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078437258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.204723, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://dev.psp.com/internal/invoices/882c6ab9-9938-4766-ac39-cbd8b6de1304", "action_name": "filament.internal.resources.invoices.view", "controller_action": "App\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice", "uri": "GET internal/invoices/{record}", "controller": "App\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice@render<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "internal/invoices", "file": "<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:internal, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "telescope": "<a href=\"https://dev.psp.com/_debugbar/telescope/9f6d32e8-30e5-419c-ad28-9be2543c3b78\" target=\"_blank\">View in Telescope</a>", "duration": "1.19s", "peak_memory": "14MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1695 characters\">remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5QMVd4eVAzYWVDZTJPbU1IOC9lVUE9PSIsInZhbHVlIjoiOXJ2dHFYRUd1WU1TRis0Njg2VHRKV3FlTUhPR3ZOZ0FMSzVTdG9SK2RWTCsvOEViMEFCbTdBdHRTc1h4WXZSb0lvU2x2MTBFODBCaFREVlBScW9zSkhjR2xURktSMjFaUW9sYUNFVXBkUkRLQmdrVkZtQzJIOXBFVlpCVnNTY1BvNWs0YWtwMGxKS3VNdTIvQldFZWdnPT0iLCJtYWMiOiI3ODdkN2ExYmExZjU5MzE2ODBmZTU4NmRhNmQxMDVjNTUxMWE3ODEyY2EzYWVkNTU0NWUwOTk4ZmI2YjczNDdjIiwidGFnIjoiIn0%3D; remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IitDNnNWWlM0T0JuVitIL2ptLzY3WWc9PSIsInZhbHVlIjoiYkRZRFJ5MlMxbFBXL2dqNzlzQmwvQW9HVk11N3hYS3IxMHZZMkZCWWhjWko3N1kvbDYvTGU2UDZ0NE1vbHRBUUwrdUFSam15akJlVjdvbVBGWXFLVFUxVVlBWUM2UkxNVmgyWGVZU2NnL21RMjJybUZuZzFOTFN1Z3RVRUNobCtiNjJmOEs4TWZ0NGM1bittb3ZaLzNFZFVvTTRycVM5RHBLaGsyUThNVzZqdFNiYnBldVV0TWNZLzRocjcxUGZ0UUhBaG5RWXlIdEU0d3RnQUZMZVAwTldqMHJyYVBlZ0lrQlphOHNuWVpBQT0iLCJtYWMiOiI1ZjhkMDMzYzRiMzAyOWFmZWJhNThmYzYwODU3Yjc2N2NkYTg1OWJmZTkxNzUwYWVhMjg0ODQzODMxNDlkOTg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkRkS0VxY2R2VDBiYzdvdDd0c3poS3c9PSIsInZhbHVlIjoiYzdQL0hDZm80QVcydGJtcWxvS0I0SG1BaTRhdnhJY3VRVDRFT253QnJaSWJubHQ2VURaZnNpRHZoKzNLNmtreGQ4bDF6YVg0TGo0VGtVSVdEWTd2QWw1UzVlaUdMcmFwWU4yM3BnQXBwS0ZOdFJwQVFPUHhpdVFVdUZMMWVORHoiLCJtYWMiOiJmOWJlNjRjMDU2ZGQwZWFlMDZhNDNiNzVmZDkwOGZjNTAwOWVjYWM2NmJhMjIxOWJlYWJmMzA2ZjczMDZjYjU4IiwidGFnIjoiIn0%3D; ceval_app_session=eyJpdiI6ImFrT25pcFJrYTU5NktDbTdJb2l4NWc9PSIsInZhbHVlIjoiK2loRHNJRVUxVWZiNWF6akxoUjczemVUcGN6Q2ZVOHRCelV6OEZrOXpmWGF5NldsVkMxZTBEcS9BcDV6ZXNablNXdkR4NFdEZjUxK04ybkZBTnk1NGp5VE9kUENIZWM2dTNyUHBYd2N3WWR5bEkyakY2dlVkZ00vR3hIc0xDWVQiLCJtYWMiOiI4MDI1ZTFmMGRkMTllNmIxNDlkNTdjNzFlZGE5MzkzODE5YzYzOTE2YzFiMWE5NzgyNzdjMzM2MjlhMDBmNmMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,id;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">https://dev.psp.com/internal/invoices</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire-navigate</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">dev.psp.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1058378155 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">4|q5hZpWB7QSHiPq0hTFEb4e5IyIUPCzdE5GMeANTAcy4FtWS6lj04ZIiQL38G|</span>\"\n  \"<span class=sf-dump-key>remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">5|bQicjYEg6cF5oW38bBsKPo6sbK55hIXGXRCTgCFFQxCRgmArAxSeD8RHgEnv|$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>ceval_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TBncuK1Z7kHcGjnRHlb51hN4z6hEodSBkwEbeDjj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058378155\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-646862991 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 08:25:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6InJlczRwK0RDbndmS2JnWUZOcTFRb2c9PSIsInZhbHVlIjoieVgzb2pocHYwMzVFeGpkUHViZDNPcDBmSEd3c0tvL3NBWFRidngxM0FEVnF6VHRMM29sSThDdU15dlZFYU1Wdmpkdkd6UCtQWktNREltS21vaVNGa1BhMURhU2JnWDVIM09VNERERFRpZUQxRll6clhlSFIzSjFBbTA0ZllnUjEiLCJtYWMiOiJiZjc2MTgyMWQ4NDdmZDY3MTQyODg3YjM3MWI3NjY0ZDE5NjU3NmJiNmQwZjUwYjY3OGNjNmIxMGI5OTIwMTk0IiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 10:25:43 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"453 characters\">ceval_app_session=eyJpdiI6Im8zdG9RVEU0NitvZEdFQkZZeDBDT1E9PSIsInZhbHVlIjoiTzBwWVpEOWZqWlQ3SVl4a1JaT2t0V1k1Z0FBd2lDRFk0WkExYjJrNllIRkY3a0VpcVY4c2JoblBKZkFKbnA4OTBxMW10ZTB0eFRPME9wVXlDM3NkTkNxaWpWY0Y4NVpyTVdiQ2dCaUZNdTVJS3VUYTVxMEQ0cVFjaHpQWUtjOUkiLCJtYWMiOiJmMDZjMTAwM2RjYjdiNDQyZmUzNWFhNzQ3OTcxNWI3ZmE0MzAxODdhZDQ4NGViMjc1NDAwMzAzN2VhODBkMTZiIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 10:25:43 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6InJlczRwK0RDbndmS2JnWUZOcTFRb2c9PSIsInZhbHVlIjoieVgzb2pocHYwMzVFeGpkUHViZDNPcDBmSEd3c0tvL3NBWFRidngxM0FEVnF6VHRMM29sSThDdU15dlZFYU1Wdmpkdkd6UCtQWktNREltS21vaVNGa1BhMURhU2JnWDVIM09VNERERFRpZUQxRll6clhlSFIzSjFBbTA0ZllnUjEiLCJtYWMiOiJiZjc2MTgyMWQ4NDdmZDY3MTQyODg3YjM3MWI3NjY0ZDE5NjU3NmJiNmQwZjUwYjY3OGNjNmIxMGI5OTIwMTk0IiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 10:25:43 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"425 characters\">ceval_app_session=eyJpdiI6Im8zdG9RVEU0NitvZEdFQkZZeDBDT1E9PSIsInZhbHVlIjoiTzBwWVpEOWZqWlQ3SVl4a1JaT2t0V1k1Z0FBd2lDRFk0WkExYjJrNllIRkY3a0VpcVY4c2JoblBKZkFKbnA4OTBxMW10ZTB0eFRPME9wVXlDM3NkTkNxaWpWY0Y4NVpyTVdiQ2dCaUZNdTVJS3VUYTVxMEQ0cVFjaHpQWUtjOUkiLCJtYWMiOiJmMDZjMTAwM2RjYjdiNDQyZmUzNWFhNzQ3OTcxNWI3ZmE0MzAxODdhZDQ4NGViMjc1NDAwMzAzN2VhODBkMTZiIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 10:25:43 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646862991\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1864997500 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>password_hash_internal</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>ListOrders_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"74 characters\">https://dev.psp.com/internal/invoices/882c6ab9-9938-4766-ac39-cbd8b6de1304</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>ListSubscriptions_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-28</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-12-31</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864997500\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://dev.psp.com/internal/invoices/882c6ab9-9938-4766-ac39-cbd8b6de1304", "action_name": "filament.internal.resources.invoices.view", "controller_action": "App\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ViewInvoice"}, "badge": null}}