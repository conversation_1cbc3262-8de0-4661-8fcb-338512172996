{"__meta": {"id": "01K0GVH83BRAPWHE937SWPCCJZ", "datetime": "2025-07-19 15:06:44", "utime": **********.587572, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.530332, "end": **********.587595, "duration": 1.057262897491455, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": **********.530332, "relative_start": 0, "end": **********.860169, "relative_end": **********.860169, "duration": 0.****************, "duration_str": "330ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.860193, "relative_start": 0.****************, "end": **********.587598, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "727ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.248457, "relative_start": 0.****************, "end": **********.249318, "relative_end": **********.249318, "duration": 0.0008609294891357422, "duration_str": "861μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.468034, "relative_start": 0.***************, "end": **********.468034, "relative_end": **********.468034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.475843, "relative_start": 0.****************, "end": **********.475843, "relative_end": **********.475843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.494224, "relative_start": 0.9638919830322266, "end": **********.494224, "relative_end": **********.494224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.499265, "relative_start": 0.9689328670501709, "end": **********.499265, "relative_end": **********.499265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.506338, "relative_start": 0.9760057926177979, "end": **********.506338, "relative_end": **********.506338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.508662, "relative_start": 0.9783298969268799, "end": **********.508662, "relative_end": **********.508662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.522567, "relative_start": 0.9922349452972412, "end": **********.522567, "relative_end": **********.522567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.586361, "relative_start": 1.0560288429260254, "end": **********.587101, "relative_end": **********.587101, "duration": 0.00074005126953125, "duration_str": "740μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8412632, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.18", "Environment": "local", "Debug Mode": "Enabled", "URL": "dev.psp.com", "Timezone": "Asia/Jakarta", "Locale": "en"}}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.467983, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "livewire::tailwind", "param_count": null, "params": [], "start": **********.475795, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.494172, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.499213, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.506278, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.50861, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.522528, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}]}, "queries": {"count": 18, "nb_statements": 18, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05438, "accumulated_duration_str": "54.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 5 and `user_type` = 'admin' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.255857, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "psp_dev", "explain": null, "start_percent": 0, "width_percent": 5.719}, {"sql": "select * from `plans` where `plans`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.3169792, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:55", "source": {"index": 18, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=55", "ajax": false, "filename": "ListSubscriptions.php", "line": "55"}, "connection": "psp_dev", "explain": null, "start_percent": 5.719, "width_percent": 6.363}, {"sql": "select * from `subscriptions` where `subscriptions`.`plan_id` = 1 and `subscriptions`.`plan_id` is not null and `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.326155, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:64", "source": {"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=64", "ajax": false, "filename": "ListSubscriptions.php", "line": "64"}, "connection": "psp_dev", "explain": null, "start_percent": 12.082, "width_percent": 6.804}, {"sql": "select * from `subscriptions` where `subscriptions`.`plan_id` = 2 and `subscriptions`.`plan_id` is not null and `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.331701, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:64", "source": {"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=64", "ajax": false, "filename": "ListSubscriptions.php", "line": "64"}, "connection": "psp_dev", "explain": null, "start_percent": 18.886, "width_percent": 5.572}, {"sql": "select * from `subscriptions` where `subscriptions`.`plan_id` = 3 and `subscriptions`.`plan_id` is not null and `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.337113, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:64", "source": {"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=64", "ajax": false, "filename": "ListSubscriptions.php", "line": "64"}, "connection": "psp_dev", "explain": null, "start_percent": 24.458, "width_percent": 5.296}, {"sql": "select * from `subscriptions` where `subscriptions`.`plan_id` = 4 and `subscriptions`.`plan_id` is not null and `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.34221, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:64", "source": {"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=64", "ajax": false, "filename": "ListSubscriptions.php", "line": "64"}, "connection": "psp_dev", "explain": null, "start_percent": 29.754, "width_percent": 5.186}, {"sql": "select * from `subscriptions` where `subscriptions`.`plan_id` = 7 and `subscriptions`.`plan_id` is not null and `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.3468869, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:64", "source": {"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=64", "ajax": false, "filename": "ListSubscriptions.php", "line": "64"}, "connection": "psp_dev", "explain": null, "start_percent": 34.939, "width_percent": 4.983}, {"sql": "select count(*) as aggregate from `subscriptions` where `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.372185, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "psp_dev", "explain": null, "start_percent": 39.923, "width_percent": 4.744}, {"sql": "select * from `subscriptions` where `subscriptions`.`deleted_at` is null order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.377376, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 44.667, "width_percent": 6.749}, {"sql": "select * from `users` where `users`.`id` in (4) and `user_type` = 'user' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["user"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.3832018, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 51.416, "width_percent": 5.241}, {"sql": "select * from `plans` where `plans`.`id` in (2) and `plans`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.388829, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 56.657, "width_percent": 5.554}, {"sql": "select * from `plan_prices` where `plan_prices`.`id` in (6) and `plan_prices`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.39404, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 62.21, "width_percent": 5.664}, {"sql": "select * from `plans` where `is_active` = 1 and `plans`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 74}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.551072, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "GrowthChart.php:74", "source": {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FWidgets%2FGrowthChart.php&line=74", "ajax": false, "filename": "GrowthChart.php", "line": "74"}, "connection": "psp_dev", "explain": null, "start_percent": 67.874, "width_percent": 5.241}, {"sql": "select\ndate_format(created_at, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 1 and `subscriptions`.`deleted_at` is null and `created_at` between '2025-01-28 00:00:00' and '2025-07-19 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 1, "2025-01-28 00:00:00", "2025-07-19 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 87}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.555601, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 73.115, "width_percent": 4.983}, {"sql": "select\ndate_format(created_at, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 2 and `subscriptions`.`deleted_at` is null and `created_at` between '2025-01-28 00:00:00' and '2025-07-19 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 2, "2025-01-28 00:00:00", "2025-07-19 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 87}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.560323, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 78.099, "width_percent": 5.59}, {"sql": "select\ndate_format(created_at, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 3 and `subscriptions`.`deleted_at` is null and `created_at` between '2025-01-28 00:00:00' and '2025-07-19 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 3, "2025-01-28 00:00:00", "2025-07-19 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 87}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.565357, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 83.689, "width_percent": 5.388}, {"sql": "select\ndate_format(created_at, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 4 and `subscriptions`.`deleted_at` is null and `created_at` between '2025-01-28 00:00:00' and '2025-07-19 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 4, "2025-01-28 00:00:00", "2025-07-19 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 87}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.5705051, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 89.077, "width_percent": 5.406}, {"sql": "select\ndate_format(created_at, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 7 and `subscriptions`.`deleted_at` is null and `created_at` between '2025-01-28 00:00:00' and '2025-07-19 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 7, "2025-01-28 00:00:00", "2025-07-19 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 87}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.576715, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 94.483, "width_percent": 5.517}]}, "models": {"data": {"App\\Models\\Plan": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}, "App\\Models\\Subscription": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FSubscription.php&line=1", "ajax": false, "filename": "Subscription.php", "line": "?"}}, "App\\Models\\UserInternal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserInternal.php&line=1", "ajax": false, "filename": "UserInternal.php", "line": "?"}}, "App\\Models\\UserMember": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserMember.php&line=1", "ajax": false, "filename": "UserMember.php", "line": "?"}}, "App\\Models\\PlanPrice": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FPlanPrice.php&line=1", "ajax": false, "filename": "PlanPrice.php", "line": "?"}}}, "count": 16, "is_counter": true}, "livewire": {"data": {"app.filament.internal.resources.subscription-resource.pages.list-subscriptions #eto1ozBNwD0xSnBZkcmj": "array:4 [\n  \"data\" => array:39 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => null\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => \"0\"\n    \"mountedActions\" => array:1 [\n      0 => \"filter\"\n    ]\n    \"mountedActionsArguments\" => array:1 [\n      0 => []\n    ]\n    \"mountedActionsData\" => array:1 [\n      0 => array:2 [\n        \"startDate\" => \"2025-01-28\"\n        \"endDate\" => \"2025-07-19\"\n      ]\n    ]\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n    \"filters\" => array:2 [\n      \"startDate\" => \"2025-01-28\"\n      \"endDate\" => \"2025-07-19\"\n    ]\n  ]\n  \"name\" => \"app.filament.internal.resources.subscription-resource.pages.list-subscriptions\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions\"\n  \"id\" => \"eto1ozBNwD0xSnBZkcmj\"\n]", "app.filament.internal.resources.subscription-resource.widgets.stats-overview #rLNQCRwJyMF9DIgIzzVt": "array:4 [\n  \"data\" => array:1 [\n    \"filters\" => array:2 [\n      \"startDate\" => \"2025-01-28\"\n      \"endDate\" => \"2025-07-19\"\n    ]\n  ]\n  \"name\" => \"app.filament.internal.resources.subscription-resource.widgets.stats-overview\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\StatsOverview\"\n  \"id\" => \"rLNQCRwJyMF9DIgIzzVt\"\n]", "app.filament.internal.resources.subscription-resource.widgets.growth-chart #MhpqvpxlQp5TebvyZX9k": "array:4 [\n  \"data\" => array:3 [\n    \"dataChecksum\" => \"896bece39e49a5873fc0dbc64aa98fcb\"\n    \"filter\" => null\n    \"filters\" => array:2 [\n      \"startDate\" => \"2025-01-28\"\n      \"endDate\" => \"2025-07-19\"\n    ]\n  ]\n  \"name\" => \"app.filament.internal.resources.subscription-resource.widgets.growth-chart\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart\"\n  \"id\" => \"MhpqvpxlQp5TebvyZX9k\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://dev.psp.com/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions@mountAction<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=159\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=159\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/actions/src/Concerns/InteractsWithActions.php:159-212</a>", "middleware": "web", "telescope": "<a href=\"https://dev.psp.com/_debugbar/telescope/9f6d2c1e-ad52-4e00-bfcf-d5c913400008\" target=\"_blank\">View in Telescope</a>", "duration": "1.06s", "peak_memory": "12MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1443851253 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1443851253\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1503973719 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1969 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:null,&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:&quot;0&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:[{&quot;startDate&quot;:&quot;2025-01-28&quot;,&quot;endDate&quot;:&quot;2025-07-19&quot;},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;eto1ozBNwD0xSnBZkcmj&quot;,&quot;name&quot;:&quot;app.filament.internal.resources.subscription-resource.pages.list-subscriptions&quot;,&quot;path&quot;:&quot;internal\\/subscriptions&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;App\\\\Filament\\\\Internal\\\\Resources\\\\SubscriptionResource\\\\Widgets\\\\StatsOverview-0&quot;:[&quot;div&quot;,&quot;rLNQCRwJyMF9DIgIzzVt&quot;],&quot;App\\\\Filament\\\\Internal\\\\Resources\\\\SubscriptionResource\\\\Widgets\\\\GrowthChart-1&quot;:[&quot;div&quot;,&quot;MhpqvpxlQp5TebvyZX9k&quot;]},&quot;scripts&quot;:[&quot;779550052-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;d3d55b46b17f121877d8641f7d94003c70bf7d2595cff27b0d55450f5b2e91ab&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"11 characters\">mountAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">filter</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"436 characters\">{&quot;data&quot;:{&quot;filters&quot;:[{&quot;startDate&quot;:&quot;2025-01-28&quot;,&quot;endDate&quot;:&quot;2025-07-19&quot;},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;rLNQCRwJyMF9DIgIzzVt&quot;,&quot;name&quot;:&quot;app.filament.internal.resources.subscription-resource.widgets.stats-overview&quot;,&quot;path&quot;:&quot;internal\\/subscriptions&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:true,&quot;props&quot;:[&quot;filters&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;04e388358599b5d0ee0a02baea15f91c8161e04b3caef8d9fa64fa3c3205aca6&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"498 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;********************************&quot;,&quot;filter&quot;:null,&quot;filters&quot;:[{&quot;startDate&quot;:&quot;2025-01-28&quot;,&quot;endDate&quot;:&quot;2025-07-19&quot;},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;MhpqvpxlQp5TebvyZX9k&quot;,&quot;name&quot;:&quot;app.filament.internal.resources.subscription-resource.widgets.growth-chart&quot;,&quot;path&quot;:&quot;internal\\/subscriptions&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:true,&quot;props&quot;:[&quot;filters&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;b647aeaca84254f7b64bfe971bbc5531f34e0b1992f86770565c055c969b2b48&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1503973719\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1349522112 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1695 characters\">remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5QMVd4eVAzYWVDZTJPbU1IOC9lVUE9PSIsInZhbHVlIjoiOXJ2dHFYRUd1WU1TRis0Njg2VHRKV3FlTUhPR3ZOZ0FMSzVTdG9SK2RWTCsvOEViMEFCbTdBdHRTc1h4WXZSb0lvU2x2MTBFODBCaFREVlBScW9zSkhjR2xURktSMjFaUW9sYUNFVXBkUkRLQmdrVkZtQzJIOXBFVlpCVnNTY1BvNWs0YWtwMGxKS3VNdTIvQldFZWdnPT0iLCJtYWMiOiI3ODdkN2ExYmExZjU5MzE2ODBmZTU4NmRhNmQxMDVjNTUxMWE3ODEyY2EzYWVkNTU0NWUwOTk4ZmI2YjczNDdjIiwidGFnIjoiIn0%3D; remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IitDNnNWWlM0T0JuVitIL2ptLzY3WWc9PSIsInZhbHVlIjoiYkRZRFJ5MlMxbFBXL2dqNzlzQmwvQW9HVk11N3hYS3IxMHZZMkZCWWhjWko3N1kvbDYvTGU2UDZ0NE1vbHRBUUwrdUFSam15akJlVjdvbVBGWXFLVFUxVVlBWUM2UkxNVmgyWGVZU2NnL21RMjJybUZuZzFOTFN1Z3RVRUNobCtiNjJmOEs4TWZ0NGM1bittb3ZaLzNFZFVvTTRycVM5RHBLaGsyUThNVzZqdFNiYnBldVV0TWNZLzRocjcxUGZ0UUhBaG5RWXlIdEU0d3RnQUZMZVAwTldqMHJyYVBlZ0lrQlphOHNuWVpBQT0iLCJtYWMiOiI1ZjhkMDMzYzRiMzAyOWFmZWJhNThmYzYwODU3Yjc2N2NkYTg1OWJmZTkxNzUwYWVhMjg0ODQzODMxNDlkOTg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImFCR2daN2FBR0c2YnozSEJDSnIrYmc9PSIsInZhbHVlIjoidU9MeEoycnZDcnp5MGtzRG1qc2F5YVMyQVhMamlmNkU3VEpTK3lGWGJMSWhnRU5JN3Jtb0xOczdnaWlEeFllZHBvVGV5ZS9CRWFYejdJT2xEeCtMYzZFTWU1bVRGRzExcW5VdVZnaERKTTdnczRqczRuZEhrVmFvdmFWckcxK1AiLCJtYWMiOiI3ZjNlYjc3N2FiMDVjYjE5ODFjNmVlNzNkYzdmNmNiZDFiOGY3MDhmNjg3MTRjYThkZGRmN2YyMmM4NTNhZmFjIiwidGFnIjoiIn0%3D; ceval_app_session=eyJpdiI6IlVDcUtINWhkazJodDNGSkdXL1lCVGc9PSIsInZhbHVlIjoieUVDeWFpaFNnTEpnK0dVZVg5bWtVQkhXSEJQaTMwZHNlRG1jS1hRbFAvNXhmREl6YlIvN05FRmhLakd6ZmNsendrU05ES2lnRWcxQlFJWUtrbE9yYUJaTGowUFk1akFkeTlnZzRhVk1tUHJCSXloZDA2NnRHcXZLTEhtNTIzY1kiLCJtYWMiOiJiNzZhMGRlODBmNDkyZGRmZmM0ZTY4ZjZhZGE4MGY3YThiNzZkZjU1ZjhjOWRjNjkxMTkyYzdmNjJkZmRjMjkyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,id;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"100 characters\">https://dev.psp.com/internal/subscriptions?filters[startDate]=2025-01-28&amp;filters[endDate]=2025-07-19</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://dev.psp.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3508</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">dev.psp.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349522112\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1155038364 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">4|q5hZpWB7QSHiPq0hTFEb4e5IyIUPCzdE5GMeANTAcy4FtWS6lj04ZIiQL38G|</span>\"\n  \"<span class=sf-dump-key>remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">5|bQicjYEg6cF5oW38bBsKPo6sbK55hIXGXRCTgCFFQxCRgmArAxSeD8RHgEnv|$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>ceval_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TBncuK1Z7kHcGjnRHlb51hN4z6hEodSBkwEbeDjj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155038364\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-971131702 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 08:06:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-971131702\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-952763580 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>password_hash_internal</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>ListOrders_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"108 characters\">https://dev.psp.com/internal/subscriptions?filters%5BstartDate%5D=2025-01-28&amp;filters%5BendDate%5D=2025-07-19</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>ListSubscriptions_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-28</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-19</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-952763580\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://dev.psp.com/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}