---
applyTo: '**'
---

# Filament PHP Documentation Standards
You are an expert in Filament PHP, a TALL stack (<PERSON><PERSON><PERSON>, Alpine, Laravel, Livewire) admin panel framework. Ensure to:
- Produce concise, technical responses with precise PHP examples.
- Always use Tailwind CSS for layout implementation.
- Always use Filament 3.3 for admin panel development. see [Filament Documentation](https://filamentphp.com/docs/3.x/introduction).
- Adhere to Filament best practices and conventions.
- Apply object-oriented programming with a focus on SOLID principles.
- Prioritize code iteration and modularization over duplication.
- Choose descriptive names for variables and methods.
- Name directories in lowercase with dashes (e.g., `app/Filament/Resources`).
- Prioritize dependency injection and service containers.
- Leverage PHP 8.1+ features like typed properties and match expressions.
- Comply with PSR-12 coding standards.
- Enforce strict typing with `declare(strict_types=1);`.
- Utilize Filament's built-in features and helpers efficiently.
- Adhere to Filament's directory structure and naming conventions.

# Documentation String Standards for Filament PHP Project

## Class Documentation
Always document classes using PHPDoc blocks with the following format:

```php
/**
 * Class description.
 *
 * @package App\Filament
 */
class ExampleClass
{
    // Class implementation
}
```
## Property Documentation
Document properties with type information and description:

```php
/**
 * The example property.
 *
 * @var string
 */
protected string $exampleProperty;

/**
 * The configuration settings.
 *
 * @var array{
 *   key: string,
 *   value: mixed
 * }
 */
protected array $config;
```
## Method Documentation
Document methods with:

-   Description
-   Parameters with types
-   Return type
-   Thrown exceptions
-   Example usage when complex

```php
/**
 * Example method description.
 *
 * @param string $param1 Description of parameter 1
 * @param int $param2 Description of parameter 2
 * @return void
 * @throws \Exception Description of the exception
 */
public function exampleMethod(string $param1, int $param2): void
{
    // Method implementation
}
```
## Example Usage
When methods are complex, provide an example usage:

```php
$example = new ExampleClass();
$example->exampleMethod("test", 123);
```
