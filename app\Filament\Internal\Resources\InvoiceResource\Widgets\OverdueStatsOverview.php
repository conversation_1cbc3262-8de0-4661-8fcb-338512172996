<?php

namespace App\Filament\Internal\Resources\InvoiceResource\Widgets;

use App\Enum\PaymentStatusEnum;
use App\Models\Invoice;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class OverdueStatsOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    /**
     * Get the widget heading
     *
     * @return string|null
     */
    protected function getHeading(): ?string
    {
        return 'Outstanding Invoices';
    }

    /**
     * Get the stats to display in the widget
     *
     * @return array Array of Stat objects
     */
    protected function getStats(): array
    {
        $pendingInvoices = Invoice::where('payment_status', PaymentStatusEnum::PENDING->value);

        $current = $pendingInvoices->clone()->where('due_date', '>', now());
        $overdue = $pendingInvoices->clone()->where('due_date', '<=', now());

        $currentCount = $current->count();
        $overdueCount = $overdue->count();

        $currentAmount = $current->sum('total_amount');
        $overdueAmount = $overdue->sum('total_amount');
        $total = $currentAmount + $overdueAmount;
        $totalCount = $currentCount + $overdueCount;

        return [
            Stat::make('Current', $currentCount)
                ->description('IDR ' . number_format($currentAmount, 2, ',', '.')),

            Stat::make('Over Due', $overdueCount)
                ->description('IDR ' . number_format($overdueAmount, 2, ',', '.')),

            Stat::make('Total', 'IDR ' . number_format($total, 2, ',', '.'))
                ->description($totalCount . ' Invoice(s)'),
        ];
    }
}
