<?php

namespace App\Filament\Internal\Resources\InvoiceResource\Widgets;

use App\Models\Invoice;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class PaidStatsOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    /**
     * Get the widget heading
     *
     * @return string|null
     */
    protected function getHeading(): ?string
    {
        return 'Paid Invoices';
    }

    /**
     * Get the stats to display in the widget
     *
     * @return array Array of Stat objects
     */
    protected function getStats(): array
    {
        $paid = $this->getPaid();
        $totalInvoice = $this->getTotalInvoice();

        $paidInvoice = $this->getTotalPaidInvoice();
        $average = $paid / $paidInvoice;
        $ppn = $paid * (11 / 100);

        $average = 'IDR ' . number_format($average, 2, ',', '.');
        $paid = 'IDR ' . number_format($paid, 2, ',', '.');
        $ppn = 'IDR ' . number_format($ppn, 2, ',', '.');


        return [
            Stat::make('Total Paid', $paid),
            Stat::make('Total Invoices', $totalInvoice)
                ->description($paidInvoice . ' Paid'),
            Stat::make('Average', $average),
            Stat::make('PPN', $ppn)
                ->description('11% PPN'),

        ];
    }

    /**
     * Get the total amount of paid invoices
     *
     * @return int
     */
    private function getPaid(): int
    {
        return Invoice::where('payment_status', 'paid')->sum('total_amount');
    }

    /**
     * Get the total number of invoices
     *
     * @return int Total count of invoices
     */
    private function getTotalInvoice(): int
    {
        return Invoice::count();
    }

    /**
     * Get the total number of paid invoices
     *
     * @return int Total count of paid invoices
     */
    private function getTotalPaidInvoice(): int
    {
        return Invoice::where('payment_status', 'paid')->count();
    }
}
