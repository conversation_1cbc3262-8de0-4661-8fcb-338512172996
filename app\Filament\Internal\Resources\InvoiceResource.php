<?php

namespace App\Filament\Internal\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Invoice;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Enum\PaymentStatusEnum;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Internal\Resources\InvoiceResource\Pages;
use App\Filament\Internal\Resources\InvoiceResource\RelationManagers;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'Ordering';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('invoice_date', 'desc')
            ->paginated([10, 25, 50])
            ->columns([
                TextColumn::make('invoice_number')
                    ->searchable(),
                TextColumn::make('user.name')
                    ->label('Customer Name')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('user.name')
                    ->label('Customer Name')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('type')
                    ->label('Invoice Type')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('invoice_date')
                    ->dateTime('d M Y ')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('total_amount')
                    ->sortable()
                    ->money('IDR'),
                TextColumn::make('payment_status')
                    ->label('Payment Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        PaymentStatusEnum::PENDING->value => 'orange',
                        PaymentStatusEnum::PROCESSING->value => 'orange',
                        PaymentStatusEnum::EXPIRED->value => 'red',
                        PaymentStatusEnum::CANCELLED->value => 'gray',
                        PaymentStatusEnum::PAID->value => 'green',
                        PaymentStatusEnum::FAILED->value => 'red',
                        PaymentStatusEnum::REFUNDED->value => 'black'
                    }),
                TextColumn::make('payment_date')->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->button()->color('primary'),
                //Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'create' => Pages\CreateInvoice::route('/create'),
            'view' => Pages\ViewInvoice::route('/{record}'),
            'edit' => Pages\EditInvoice::route('/{record}/edit'),
        ];
    }
}
