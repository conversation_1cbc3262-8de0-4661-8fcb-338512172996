<?php

namespace App\Filament\Internal\Resources;

use Filament\Forms;
use App\Models\Plan;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\Subscription;
use App\Enum\PaymentStatusEnum;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Placeholder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Internal\Resources\SubscriptionResource\Pages;
use App\Filament\Internal\Resources\SubscriptionResource\Widgets\StatsOverview;
use App\Filament\Internal\Resources\SubscriptionResource\Forms\SubscriptionForm;
use App\Filament\Internal\Resources\SubscriptionResource\RelationManagers\InvoicesRelationManager;
use Filament\Tables\Filters\SelectFilter;

class SubscriptionResource extends Resource
{
    protected static ?string $model = Subscription::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-path';
    protected static ?string $navigationGroup = 'Ordering';

    public static function form(Form $form): Form
    {
        return $form
            ->columns(4)
            ->schema(
                [
                    Group::make()
                        ->columnSpan(['lg' => fn(?Model $record) => $record === null ? 4 : 3])
                        ->schema(SubscriptionForm::getFormSchema()),
                    Group::make()
                        ->columnSpan(['lg' => 1])
                        ->schema([
                            Section::make('User Information')
                                ->columns(2)
                                ->schema([
                                    Placeholder::make('user.name')
                                        ->label('Name')
                                        ->content(function (Model $record): HtmlString {
                                            $user = $record->user?->name ?? 'Not created yet';
                                            $url = route('filament.internal.resources.user-members.view', [
                                                'record' => $record->user->uuid,
                                            ]);
                                            return new HtmlString('<a href="' . $url . '">' . $user . '</a>');
                                        }),
                                    Placeholder::make('user.email')
                                        ->label('Email')
                                        ->content(fn(Model $record): ?string => $record->user->email),
                                    Placeholder::make('user.phone')
                                        ->label('Phone'),
                                ]),
                            Section::make()
                                ->columns(2)
                                ->schema([
                                    Placeholder::make('created_at')
                                        ->label('Created at')
                                        ->content(fn(Model $record): ?string => $record->created_at?->diffForHumans()),
                                    Placeholder::make('createdByUser.name')
                                        ->label('Created by')
                                        ->hintIconTooltip('filamentphp.com')
                                        ->content(function (Model $record): HtmlString {
                                            $user = $record->createdByUser?->name ?? 'Not created yet';
                                            $url = route('filament.internal.resources.user-members.view', [
                                                'record' => $record->createdByUser->uuid,
                                            ]);
                                            return new HtmlString('<a href="' . $url . '">' . $user . '</a>');
                                        }),
                                    Placeholder::make('updated_at')
                                        ->label('Last modified at')
                                        ->content(fn(Model $record): ?string => $record->updated_at?->diffForHumans()),
                                    Placeholder::make('updatedByUser.name')
                                        ->label('Last modified by')
                                        ->content(function (Model $record): HtmlString {
                                            $user = $record->updatedByUser?->name ?? 'Not created yet';
                                            $url = $record->updatedByUser
                                                ? route('filament.internal.resources.user-members.view', [
                                                    'record' => $record->updatedByUser->uuid,
                                                ])
                                                : '';
                                            return new HtmlString('<a href="' . $url . '">' . $user . '</a>');
                                        }),
                                ])
                                ->columnSpan(['lg' => 1])
                                ->hidden(fn(?Model $record) => $record === null)
                        ]),
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('user.name')->sortable()->searchable(),
                TextColumn::make('plan.name')
                    ->label('Plan Name')
                    ->sortable()->searchable(),
                TextColumn::make('planPrice.price_name')->sortable()->searchable(),
                TextColumn::make('payment_status')
                    ->label('Payment Status')
                    ->sortable()
                    ->searchable()
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        PaymentStatusEnum::PAID->value => 'success',
                        PaymentStatusEnum::PENDING->value => 'warning',
                        PaymentStatusEnum::FAILED->value => 'danger',
                        PaymentStatusEnum::EXPIRED->value => 'danger',
                    }),
                TextColumn::make('start_date')
                    ->label('Start Date')
                    ->dateTime('Y-m-d')
                    ->sortable()->searchable(),
                TextColumn::make('end_date')
                    ->label('End Date')
                    ->dateTime('Y-m-d')
                    ->sortable()->searchable(),
                TextColumn::make('created_at')->dateTime()->sortable()->searchable(),
            ])
            ->filters([
                SelectFilter::make('plan_id')
                    ->label('Plan')
                    ->options(function () {
                        return Plan::where('is_active', true)
                            ->get()
                            ->pluck('name', 'id');
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->button()->color('primary'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            InvoicesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptions::route('/'),
            'create' => Pages\CreateSubscription::route('/create'),
            'view' => Pages\ViewSubscription::route('/{record}'),
            'edit' => Pages\EditSubscription::route('/{record}/edit'),
        ];
    }
}
