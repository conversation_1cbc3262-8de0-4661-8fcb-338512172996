{"__meta": {"id": "01K0GVWTN8MAJYXJDTCX3FTE73", "datetime": "2025-07-19 15:13:04", "utime": **********.040928, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.170915, "end": **********.040949, "duration": 0.8700342178344727, "duration_str": "870ms", "measures": [{"label": "Booting", "start": **********.170915, "relative_start": 0, "end": **********.506109, "relative_end": **********.506109, "duration": 0.*****************, "duration_str": "335ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.506135, "relative_start": 0.****************, "end": **********.040951, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "535ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.918476, "relative_start": 0.****************, "end": **********.919833, "relative_end": **********.919833, "duration": 0.0013568401336669922, "duration_str": "1.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.988675, "relative_start": 0.****************, "end": **********.988675, "relative_end": **********.988675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.039991, "relative_start": 0.****************, "end": **********.040487, "relative_end": **********.040487, "duration": 0.0004961490631103516, "duration_str": "496μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8411976, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.18", "Environment": "local", "Debug Mode": "Enabled", "URL": "dev.psp.com", "Timezone": "Asia/Jakarta", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.988568, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}]}, "queries": {"count": 9, "nb_statements": 9, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02225, "accumulated_duration_str": "22.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 5 and `user_type` = 'admin' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.926698, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "psp_dev", "explain": null, "start_percent": 0, "width_percent": 16.854}, {"sql": "select * from `plans` where `plans`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.9545672, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:57", "source": {"index": 18, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=57", "ajax": false, "filename": "ListSubscriptions.php", "line": "57"}, "connection": "psp_dev", "explain": null, "start_percent": 16.854, "width_percent": 9.933}, {"sql": "select count(*) as aggregate from `subscriptions` where `payment_status` = 'pending' and `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.967175, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "psp_dev", "explain": null, "start_percent": 26.787, "width_percent": 13.888}, {"sql": "select * from `plans` where `is_active` = 1 and `plans`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 74}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.011516, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "GrowthChart.php:74", "source": {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FWidgets%2FGrowthChart.php&line=74", "ajax": false, "filename": "GrowthChart.php", "line": "74"}, "connection": "psp_dev", "explain": null, "start_percent": 40.674, "width_percent": 9.393}, {"sql": "select\ndate_format(start_date, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 1 and `subscriptions`.`deleted_at` is null and `start_date` between '2025-01-28 00:00:00' and '2025-12-31 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 1, "2025-01-28 00:00:00", "2025-12-31 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.015721, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 50.067, "width_percent": 10.787}, {"sql": "select\ndate_format(start_date, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 2 and `subscriptions`.`deleted_at` is null and `start_date` between '2025-01-28 00:00:00' and '2025-12-31 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 2, "2025-01-28 00:00:00", "2025-12-31 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.020574, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 60.854, "width_percent": 10.292}, {"sql": "select\ndate_format(start_date, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 3 and `subscriptions`.`deleted_at` is null and `start_date` between '2025-01-28 00:00:00' and '2025-12-31 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 3, "2025-01-28 00:00:00", "2025-12-31 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.024642, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 71.146, "width_percent": 9.483}, {"sql": "select\ndate_format(start_date, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 4 and `subscriptions`.`deleted_at` is null and `start_date` between '2025-01-28 00:00:00' and '2025-12-31 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 4, "2025-01-28 00:00:00", "2025-12-31 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.028943, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 80.629, "width_percent": 9.618}, {"sql": "select\ndate_format(start_date, '%Y-%m') as date,\ncount(*) as aggregate\nfrom `subscriptions` where `payment_status` = 'paid' and `plan_id` = 7 and `subscriptions`.`deleted_at` is null and `start_date` between '2025-01-28 00:00:00' and '2025-12-31 00:00:00' group by `date` order by `date` asc", "type": "query", "params": [], "bindings": ["paid", 7, "2025-01-28 00:00:00", "2025-12-31 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, {"index": 14, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 137}, {"index": 15, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Widgets/GrowthChart.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.03266, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "Trend.php:110", "source": {"index": 13, "namespace": null, "name": "vendor/flowframe/laravel-trend/src/Trend.php", "file": "D:\\WebData\\www\\psp\\vendor\\flowframe\\laravel-trend\\src\\Trend.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fflowframe%2Flaravel-trend%2Fsrc%2FTrend.php&line=110", "ajax": false, "filename": "Trend.php", "line": "110"}, "connection": "psp_dev", "explain": null, "start_percent": 90.247, "width_percent": 9.753}]}, "models": {"data": {"App\\Models\\Plan": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}, "App\\Models\\UserInternal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserInternal.php&line=1", "ajax": false, "filename": "UserInternal.php", "line": "?"}}}, "count": 11, "is_counter": true}, "livewire": {"data": {"app.filament.internal.resources.subscription-resource.pages.list-subscriptions #SwvQokX5jUTYkK4Kr5jG": "array:4 [\n  \"data\" => array:39 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => null\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => \"2\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n    \"filters\" => array:2 [\n      \"startDate\" => \"2025-01-28\"\n      \"endDate\" => \"2025-12-31\"\n    ]\n  ]\n  \"name\" => \"app.filament.internal.resources.subscription-resource.pages.list-subscriptions\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions\"\n  \"id\" => \"SwvQokX5jUTYkK4Kr5jG\"\n]", "app.filament.internal.resources.subscription-resource.widgets.stats-overview #PylJFoDpJBm5kwikASp4": "array:4 [\n  \"data\" => array:1 [\n    \"filters\" => array:2 [\n      \"startDate\" => \"2025-01-28\"\n      \"endDate\" => \"2025-12-31\"\n    ]\n  ]\n  \"name\" => \"app.filament.internal.resources.subscription-resource.widgets.stats-overview\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\StatsOverview\"\n  \"id\" => \"PylJFoDpJBm5kwikASp4\"\n]", "app.filament.internal.resources.subscription-resource.widgets.growth-chart #kw7h6hWuS2tfiFelrldj": "array:4 [\n  \"data\" => array:3 [\n    \"dataChecksum\" => \"7e089a4e93ad6bde5ca88fabed0b7b86\"\n    \"filter\" => null\n    \"filters\" => array:2 [\n      \"startDate\" => \"2025-01-28\"\n      \"endDate\" => \"2025-12-31\"\n    ]\n  ]\n  \"name\" => \"app.filament.internal.resources.subscription-resource.widgets.growth-chart\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\SubscriptionResource\\Widgets\\GrowthChart\"\n  \"id\" => \"kw7h6hWuS2tfiFelrldj\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://dev.psp.com/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "telescope": "<a href=\"https://dev.psp.com/_debugbar/telescope/9f6d2e61-aca4-4c9b-9d04-060f3be1ed27\" target=\"_blank\">View in Telescope</a>", "duration": "872ms", "peak_memory": "12MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1932329845 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1932329845\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1738899801 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1969 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:null,&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:&quot;1&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:[{&quot;startDate&quot;:&quot;2025-01-28&quot;,&quot;endDate&quot;:&quot;2025-12-31&quot;},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;SwvQokX5jUTYkK4Kr5jG&quot;,&quot;name&quot;:&quot;app.filament.internal.resources.subscription-resource.pages.list-subscriptions&quot;,&quot;path&quot;:&quot;internal\\/subscriptions&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;App\\\\Filament\\\\Internal\\\\Resources\\\\SubscriptionResource\\\\Widgets\\\\StatsOverview-0&quot;:[&quot;div&quot;,&quot;PylJFoDpJBm5kwikASp4&quot;],&quot;App\\\\Filament\\\\Internal\\\\Resources\\\\SubscriptionResource\\\\Widgets\\\\GrowthChart-1&quot;:[&quot;div&quot;,&quot;kw7h6hWuS2tfiFelrldj&quot;]},&quot;scripts&quot;:[&quot;779550052-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;90a010e8cd214bd22a32f31b31d63890ee353ced1b62efa488ffdb343ff75b9b&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>activeTab</span>\" => \"<span class=sf-dump-str>2</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"418 characters\">{&quot;data&quot;:{&quot;filters&quot;:[{&quot;startDate&quot;:&quot;2025-01-28&quot;,&quot;endDate&quot;:&quot;2025-12-31&quot;},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;PylJFoDpJBm5kwikASp4&quot;,&quot;name&quot;:&quot;app.filament.internal.resources.subscription-resource.widgets.stats-overview&quot;,&quot;path&quot;:&quot;internal\\/subscriptions&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;props&quot;:[&quot;filters&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c2bca0ca1c7fa1b1f8b9f37fa8138c192e34a96d4b96ba1f3de180485d80972b&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"480 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;3507f0aab6488a34c2e4ae1a22f5ffb4&quot;,&quot;filter&quot;:null,&quot;filters&quot;:[{&quot;startDate&quot;:&quot;2025-01-28&quot;,&quot;endDate&quot;:&quot;2025-12-31&quot;},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;kw7h6hWuS2tfiFelrldj&quot;,&quot;name&quot;:&quot;app.filament.internal.resources.subscription-resource.widgets.growth-chart&quot;,&quot;path&quot;:&quot;internal\\/subscriptions&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;props&quot;:[&quot;filters&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;51a87aa7b18f039720567621c689e0f81038427fe512ad13d247a485dd898821&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1738899801\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1642022202 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1695 characters\">remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5QMVd4eVAzYWVDZTJPbU1IOC9lVUE9PSIsInZhbHVlIjoiOXJ2dHFYRUd1WU1TRis0Njg2VHRKV3FlTUhPR3ZOZ0FMSzVTdG9SK2RWTCsvOEViMEFCbTdBdHRTc1h4WXZSb0lvU2x2MTBFODBCaFREVlBScW9zSkhjR2xURktSMjFaUW9sYUNFVXBkUkRLQmdrVkZtQzJIOXBFVlpCVnNTY1BvNWs0YWtwMGxKS3VNdTIvQldFZWdnPT0iLCJtYWMiOiI3ODdkN2ExYmExZjU5MzE2ODBmZTU4NmRhNmQxMDVjNTUxMWE3ODEyY2EzYWVkNTU0NWUwOTk4ZmI2YjczNDdjIiwidGFnIjoiIn0%3D; remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IitDNnNWWlM0T0JuVitIL2ptLzY3WWc9PSIsInZhbHVlIjoiYkRZRFJ5MlMxbFBXL2dqNzlzQmwvQW9HVk11N3hYS3IxMHZZMkZCWWhjWko3N1kvbDYvTGU2UDZ0NE1vbHRBUUwrdUFSam15akJlVjdvbVBGWXFLVFUxVVlBWUM2UkxNVmgyWGVZU2NnL21RMjJybUZuZzFOTFN1Z3RVRUNobCtiNjJmOEs4TWZ0NGM1bittb3ZaLzNFZFVvTTRycVM5RHBLaGsyUThNVzZqdFNiYnBldVV0TWNZLzRocjcxUGZ0UUhBaG5RWXlIdEU0d3RnQUZMZVAwTldqMHJyYVBlZ0lrQlphOHNuWVpBQT0iLCJtYWMiOiI1ZjhkMDMzYzRiMzAyOWFmZWJhNThmYzYwODU3Yjc2N2NkYTg1OWJmZTkxNzUwYWVhMjg0ODQzODMxNDlkOTg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkdmdVB4Z3JzbG0vQnZPNHRlckh3K3c9PSIsInZhbHVlIjoiQmtldDZ0ckM4VHg1Y29DUHBmNzc2cDdRcmlURjQrS1JUbnlxSW1qcVM5eXV6TVZZcTJ4cU5yZjMwd2trNERuZUg2QldRenFwbGhTdHFLN0lDQnIvSm1YbWI1b0ExVjNGbXhENmVSYUhpQzNERldaY1diQlhnTnBnY292dlRFcUMiLCJtYWMiOiI3ZGQzNjE4MmQ2NmQxODFiZDE3MTZkMjQ4Y2QxNTQ4YTI4MTA1NTY4YWU4NjVjYmU4YjUzZjI2ZTI1YTZlZGIwIiwidGFnIjoiIn0%3D; ceval_app_session=eyJpdiI6IlExUnZWU0xDdzA2Yksvenc2dVBGbmc9PSIsInZhbHVlIjoiSnZJU2RhYXFXS3NZZzY1bm5xc2dWUk42VFkyUTB5UVZzakM1bVU0Qi9FUmVnU0VnaTh1Q0FaVTdmeS9QY0dMZnBRK0k1L2NWdTA3dXIxVC8zeEFzQ0NpR2Q2OVhmc1Nnenp3Nm1tOVROc2x3OVU4YzQxQXovdXZsSjJkSy9EcWEiLCJtYWMiOiJiOGExNDJhMjEwYTdiZmRjMDFlYTBmNWY2YzMwMGY4NWY2MDE4N2U3NDNkYjM3MWEyMzJjYTRiOWQ1MDFhZTk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,id;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"112 characters\">https://dev.psp.com/internal/subscriptions?filters[startDate]=2025-01-28&amp;filters[endDate]=2025-12-31&amp;activeTab=2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://dev.psp.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3429</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">dev.psp.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642022202\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-195977037 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">4|q5hZpWB7QSHiPq0hTFEb4e5IyIUPCzdE5GMeANTAcy4FtWS6lj04ZIiQL38G|</span>\"\n  \"<span class=sf-dump-key>remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">5|bQicjYEg6cF5oW38bBsKPo6sbK55hIXGXRCTgCFFQxCRgmArAxSeD8RHgEnv|$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>ceval_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TBncuK1Z7kHcGjnRHlb51hN4z6hEodSBkwEbeDjj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195977037\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1524403936 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 08:13:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1524403936\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-819577588 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>password_hash_internal</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>ListOrders_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"108 characters\">https://dev.psp.com/internal/subscriptions?filters%5BstartDate%5D=2025-01-28&amp;filters%5BendDate%5D=2025-12-31</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>ListSubscriptions_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-28</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-12-31</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819577588\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://dev.psp.com/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}