{"__meta": {"id": "01K0GWK7BDPHRRCSAY2CSYZNPJ", "datetime": "2025-07-19 15:25:17", "utime": **********.933713, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.098883, "end": **********.933732, "duration": 0.8348491191864014, "duration_str": "835ms", "measures": [{"label": "Booting", "start": **********.098883, "relative_start": 0, "end": **********.413188, "relative_end": **********.413188, "duration": 0.*****************, "duration_str": "314ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.413207, "relative_start": 0.*****************, "end": **********.933734, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "521ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.902149, "relative_start": 0.****************, "end": **********.902519, "relative_end": **********.902519, "duration": 0.000370025634765625, "duration_str": "370μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.933262, "relative_start": 0.****************, "end": **********.933461, "relative_end": **********.933461, "duration": 0.00019884109497070312, "duration_str": "199μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8402680, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.18", "Environment": "local", "Debug Mode": "Enabled", "URL": "dev.psp.com", "Timezone": "Asia/Jakarta", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00938, "accumulated_duration_str": "9.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 5 and `user_type` = 'admin' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.906505, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "psp_dev", "explain": null, "start_percent": 0, "width_percent": 33.262}, {"sql": "select sum(`total_amount`) as aggregate from `invoices` where `payment_status` = 'paid' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 43}, {"index": 17, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.920387, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "PaidStatsOverview.php:43", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FWidgets%2FPaidStatsOverview.php&line=43", "ajax": false, "filename": "PaidStatsOverview.php", "line": "43"}, "connection": "psp_dev", "explain": null, "start_percent": 33.262, "width_percent": 26.119}, {"sql": "select count(*) as aggregate from `invoices` where `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 48}, {"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 23, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.9239311, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "PaidStatsOverview.php:48", "source": {"index": 19, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FWidgets%2FPaidStatsOverview.php&line=48", "ajax": false, "filename": "PaidStatsOverview.php", "line": "48"}, "connection": "psp_dev", "explain": null, "start_percent": 59.382, "width_percent": 22.068}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'paid' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 53}, {"index": 17, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.927249, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "PaidStatsOverview.php:53", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FWidgets%2FPaidStatsOverview.php&line=53", "ajax": false, "filename": "PaidStatsOverview.php", "line": "53"}, "connection": "psp_dev", "explain": null, "start_percent": 81.45, "width_percent": 18.55}]}, "models": {"data": {"App\\Models\\UserInternal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserInternal.php&line=1", "ajax": false, "filename": "UserInternal.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"app.filament.internal.resources.invoice-resource.widgets.paid-stats-overview #cssT0mi3pwSs9sWavH1P": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.internal.resources.invoice-resource.widgets.paid-stats-overview\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview\"\n  \"id\" => \"cssT0mi3pwSs9sWavH1P\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://dev.psp.com/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "telescope": "<a href=\"https://dev.psp.com/_debugbar/telescope/9f6d32c1-8020-4219-8160-b73b3032e1f3\" target=\"_blank\">View in Telescope</a>", "duration": "835ms", "peak_memory": "12MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-886493660 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-886493660\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2101900464 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;cssT0mi3pwSs9sWavH1P&quot;,&quot;name&quot;:&quot;app.filament.internal.resources.invoice-resource.widgets.paid-stats-overview&quot;,&quot;path&quot;:&quot;internal\\/invoices&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;f309fbb5d6dedbf3899579a8fd2ffccf4720d633b79c51ba806ef191a50d1515&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"248 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbW10seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6Im83S2p2OG93alJ0QUNHdm1NM21iIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiI5MTk1NzQzMjkwMjU2MzcwOWRmYmFmN2YwZDdhOWVlMTAyOTBlZWZjMGE5MTk3NmZmMGExZGRhYWQ1MzIxYTY4In0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101900464\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1352989439 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1695 characters\">remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5QMVd4eVAzYWVDZTJPbU1IOC9lVUE9PSIsInZhbHVlIjoiOXJ2dHFYRUd1WU1TRis0Njg2VHRKV3FlTUhPR3ZOZ0FMSzVTdG9SK2RWTCsvOEViMEFCbTdBdHRTc1h4WXZSb0lvU2x2MTBFODBCaFREVlBScW9zSkhjR2xURktSMjFaUW9sYUNFVXBkUkRLQmdrVkZtQzJIOXBFVlpCVnNTY1BvNWs0YWtwMGxKS3VNdTIvQldFZWdnPT0iLCJtYWMiOiI3ODdkN2ExYmExZjU5MzE2ODBmZTU4NmRhNmQxMDVjNTUxMWE3ODEyY2EzYWVkNTU0NWUwOTk4ZmI2YjczNDdjIiwidGFnIjoiIn0%3D; remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IitDNnNWWlM0T0JuVitIL2ptLzY3WWc9PSIsInZhbHVlIjoiYkRZRFJ5MlMxbFBXL2dqNzlzQmwvQW9HVk11N3hYS3IxMHZZMkZCWWhjWko3N1kvbDYvTGU2UDZ0NE1vbHRBUUwrdUFSam15akJlVjdvbVBGWXFLVFUxVVlBWUM2UkxNVmgyWGVZU2NnL21RMjJybUZuZzFOTFN1Z3RVRUNobCtiNjJmOEs4TWZ0NGM1bittb3ZaLzNFZFVvTTRycVM5RHBLaGsyUThNVzZqdFNiYnBldVV0TWNZLzRocjcxUGZ0UUhBaG5RWXlIdEU0d3RnQUZMZVAwTldqMHJyYVBlZ0lrQlphOHNuWVpBQT0iLCJtYWMiOiI1ZjhkMDMzYzRiMzAyOWFmZWJhNThmYzYwODU3Yjc2N2NkYTg1OWJmZTkxNzUwYWVhMjg0ODQzODMxNDlkOTg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjNlRUtObVRzVys1MEpnRkVqRFBPVVE9PSIsInZhbHVlIjoiS1Vac1NqeXJwdHBLVCtoV1U3V3k0ZGNvQnBHaVpvdThVRFhoYU93aWcyVFBFN3RrY0dwa3NoTnRKbzNyb1YvZWMwcjRnUFpJd0pWVWZubktGa1pCZDVHcU5zQ1BiYXpMYXE5YmRqaW4xU25xVkVsN3owOVRNaDVEV3BTNzV6UFYiLCJtYWMiOiJhYmQ1ZDA1NzYxNGE0YTU4YjlkMzMwM2VhY2M4NTAyZDIxNTZiNzljNmJhMmRhYjk2NWJjMGJjYWFmNDgxYzAyIiwidGFnIjoiIn0%3D; ceval_app_session=eyJpdiI6Imp5RnRmOEVWQUZZUmNGdWwxdnEvZUE9PSIsInZhbHVlIjoiZlBJdTEzbmlIN0N4SXcxNEVueVNkRE96MlNzTHU3bW0rL0MwczB1bUkvODhjajlDVmo3OG8vTnpMVElCODluaUErUlg3SU96dHQxcHdkMWJTUTNqSEdrc2lHNEN4NDN1WmRFbzRTcWdTRjg2amg3TnhwRHpRd3Z4TlF3eGVwejUiLCJtYWMiOiJmZjNjNjJkZDQ2ZWIzZDk5M2IxMzczYTQ0MGZlZjUyMjIwM2RhYmNjOWEzZDk5ZTUyNDc5YmU4Yzg0YTRjZWUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,id;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">https://dev.psp.com/internal/invoices</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://dev.psp.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">803</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">dev.psp.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352989439\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1061317719 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">4|q5hZpWB7QSHiPq0hTFEb4e5IyIUPCzdE5GMeANTAcy4FtWS6lj04ZIiQL38G|</span>\"\n  \"<span class=sf-dump-key>remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">5|bQicjYEg6cF5oW38bBsKPo6sbK55hIXGXRCTgCFFQxCRgmArAxSeD8RHgEnv|$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>ceval_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TBncuK1Z7kHcGjnRHlb51hN4z6hEodSBkwEbeDjj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061317719\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1400415518 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 08:25:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400415518\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1844051487 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>password_hash_internal</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>ListOrders_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">https://dev.psp.com/internal/invoices</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>ListSubscriptions_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-28</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-12-31</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844051487\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://dev.psp.com/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}