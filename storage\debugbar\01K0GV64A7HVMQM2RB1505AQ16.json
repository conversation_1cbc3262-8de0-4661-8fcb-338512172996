{"__meta": {"id": "01K0GV64A7HVMQM2RB1505AQ16", "datetime": "2025-07-19 15:00:40", "utime": **********.263192, "method": "GET", "uri": "/internal/subscriptions", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752912038.885737, "end": **********.263215, "duration": 1.3774781227111816, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1752912038.885737, "relative_start": 0, "end": **********.228543, "relative_end": **********.228543, "duration": 0.****************, "duration_str": "343ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.228569, "relative_start": 0.***************, "end": **********.263219, "relative_end": 4.0531158447265625e-06, "duration": 1.****************, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.722749, "relative_start": 0.****************, "end": **********.72439, "relative_end": **********.72439, "duration": 0.0016410350799560547, "duration_str": "1.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.959115, "relative_start": 1.***************, "end": **********.959115, "relative_end": **********.959115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.967511, "relative_start": 1.****************, "end": **********.967511, "relative_end": **********.967511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.998041, "relative_start": 1.1123039722442627, "end": **********.998041, "relative_end": **********.998041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.025694, "relative_start": 1.1399569511413574, "end": **********.025694, "relative_end": **********.025694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e93d402ed1f3da8a07f4840136a03cb", "start": **********.11095, "relative_start": 1.2252130508422852, "end": **********.11095, "relative_end": **********.11095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.260391, "relative_start": 1.3746540546417236, "end": **********.260533, "relative_end": **********.260533, "duration": 0.00014209747314453125, "duration_str": "142μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.262496, "relative_start": 1.3767590522766113, "end": **********.262692, "relative_end": **********.262692, "duration": 0.00019598007202148438, "duration_str": "196μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 9404160, "peak_usage_str": "9MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.18", "Environment": "local", "Debug Mode": "Enabled", "URL": "dev.psp.com", "Timezone": "Asia/Jakarta", "Locale": "en"}}, "views": {"count": 5, "nb_templates": 5, "templates": [{"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.959032, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "livewire::tailwind", "param_count": null, "params": [], "start": **********.967467, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.997976, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.025654, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::5e93d402ed1f3da8a07f4840136a03cb", "param_count": null, "params": [], "start": **********.11089, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/5e93d402ed1f3da8a07f4840136a03cb.blade.php__components::5e93d402ed1f3da8a07f4840136a03cb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F5e93d402ed1f3da8a07f4840136a03cb.blade.php&line=1", "ajax": false, "filename": "5e93d402ed1f3da8a07f4840136a03cb.blade.php", "line": "?"}}]}, "queries": {"count": 18, "nb_statements": 18, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06095, "accumulated_duration_str": "60.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 5 and `user_type` = 'admin' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7297268, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "psp_dev", "explain": null, "start_percent": 0, "width_percent": 5.874}, {"sql": "select * from `plans` where `plans`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 23}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 69}], "start": **********.746146, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:55", "source": {"index": 18, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=55", "ajax": false, "filename": "ListSubscriptions.php", "line": "55"}, "connection": "psp_dev", "explain": null, "start_percent": 5.874, "width_percent": 4.676}, {"sql": "select * from `subscriptions` where `subscriptions`.`plan_id` = 1 and `subscriptions`.`plan_id` is not null and `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 44}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 23}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 69}], "start": **********.752296, "duration": 0.00272, "duration_str": "2.72ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:64", "source": {"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=64", "ajax": false, "filename": "ListSubscriptions.php", "line": "64"}, "connection": "psp_dev", "explain": null, "start_percent": 10.55, "width_percent": 4.463}, {"sql": "select * from `subscriptions` where `subscriptions`.`plan_id` = 2 and `subscriptions`.`plan_id` is not null and `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 44}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 23}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 69}], "start": **********.757105, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:64", "source": {"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=64", "ajax": false, "filename": "ListSubscriptions.php", "line": "64"}, "connection": "psp_dev", "explain": null, "start_percent": 15.012, "width_percent": 4.479}, {"sql": "select * from `subscriptions` where `subscriptions`.`plan_id` = 3 and `subscriptions`.`plan_id` is not null and `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 44}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 23}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 69}], "start": **********.763949, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:64", "source": {"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=64", "ajax": false, "filename": "ListSubscriptions.php", "line": "64"}, "connection": "psp_dev", "explain": null, "start_percent": 19.491, "width_percent": 5.217}, {"sql": "select * from `subscriptions` where `subscriptions`.`plan_id` = 4 and `subscriptions`.`plan_id` is not null and `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 44}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 23}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 69}], "start": **********.7703311, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:64", "source": {"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=64", "ajax": false, "filename": "ListSubscriptions.php", "line": "64"}, "connection": "psp_dev", "explain": null, "start_percent": 24.709, "width_percent": 4.479}, {"sql": "select * from `subscriptions` where `subscriptions`.`plan_id` = 7 and `subscriptions`.`plan_id` is not null and `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 44}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 23}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 69}], "start": **********.776712, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "ListSubscriptions.php:64", "source": {"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/SubscriptionResource/Pages/ListSubscriptions.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FSubscriptionResource%2FPages%2FListSubscriptions.php&line=64", "ajax": false, "filename": "ListSubscriptions.php", "line": "64"}, "connection": "psp_dev", "explain": null, "start_percent": 29.188, "width_percent": 5.546}, {"sql": "select count(*) as aggregate from `subscriptions` where `subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.83003, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "psp_dev", "explain": null, "start_percent": 34.733, "width_percent": 4.742}, {"sql": "select * from `subscriptions` where `subscriptions`.`deleted_at` is null order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.836272, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 39.475, "width_percent": 9.024}, {"sql": "select * from `users` where `users`.`id` in (4) and `user_type` = 'user' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["user"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.845931, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 48.499, "width_percent": 6.825}, {"sql": "select * from `plans` where `plans`.`id` in (2) and `plans`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.854964, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 55.324, "width_percent": 5.726}, {"sql": "select * from `plan_prices` where `plan_prices`.`id` in (6) and `plan_prices`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.862085, "duration": 0.00298, "duration_str": "2.98ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 61.05, "width_percent": 4.889}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (5) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\UserInternal'", "type": "query", "params": [], "bindings": ["App\\Models\\UserInternal"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.032731, "duration": 0.00315, "duration_str": "3.15ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "psp_dev", "explain": null, "start_percent": 65.939, "width_percent": 5.168}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (5) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\UserInternal'", "type": "query", "params": [], "bindings": ["App\\Models\\UserInternal"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.039273, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "psp_dev", "explain": null, "start_percent": 71.107, "width_percent": 4.381}, {"sql": "select count(*) as aggregate from `tickets` where `status` = 'open' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.077358, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "TicketResource.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FTicketResource.php&line=33", "ajax": false, "filename": "TicketResource.php", "line": "33"}, "connection": "psp_dev", "explain": null, "start_percent": 75.488, "width_percent": 5.201}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.0928671, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "psp_dev", "explain": null, "start_percent": 80.689, "width_percent": 7.137}, {"sql": "select count(*) as aggregate from `tickets` where `status` = 'open' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.16347, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "TicketResource.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FTicketResource.php&line=33", "ajax": false, "filename": "TicketResource.php", "line": "33"}, "connection": "psp_dev", "explain": null, "start_percent": 87.826, "width_percent": 4.085}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.176516, "duration": 0.0049299999999999995, "duration_str": "4.93ms", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "psp_dev", "explain": null, "start_percent": 91.911, "width_percent": 8.089}]}, "models": {"data": {"App\\Models\\Plan": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}, "App\\Models\\Subscription": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FSubscription.php&line=1", "ajax": false, "filename": "Subscription.php", "line": "?"}}, "App\\Models\\UserInternal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserInternal.php&line=1", "ajax": false, "filename": "UserInternal.php", "line": "?"}}, "App\\Models\\UserMember": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserMember.php&line=1", "ajax": false, "filename": "UserMember.php", "line": "?"}}, "App\\Models\\PlanPrice": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FPlanPrice.php&line=1", "ajax": false, "filename": "PlanPrice.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 12, "is_counter": true}, "livewire": {"data": {"app.filament.internal.resources.subscription-resource.pages.list-subscriptions #wTmXBofqViCTbpeEWjav": "array:4 [\n  \"data\" => array:39 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => null\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => \"0\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n    \"filters\" => array:2 [\n      \"startDate\" => \"2025-01-28\"\n      \"endDate\" => \"2025-07-02\"\n    ]\n  ]\n  \"name\" => \"app.filament.internal.resources.subscription-resource.pages.list-subscriptions\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions\"\n  \"id\" => \"wTmXBofqViCTbpeEWjav\"\n]", "filament.livewire.notifications #S0N4BxpYV4hhqu7w3mp3": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#4958\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"S0N4BxpYV4hhqu7w3mp3\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 20, "messages": [{"message": "[\n  ability => page_Product,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2114478618 data-indent-pad=\"  \"><span class=sf-dump-note>page_Product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">page_Product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2114478618\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.046353, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1081483745 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081483745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.058594, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1004705874 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1004705874\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.060169, "xdebug_link": null}, {"message": "[\n  ability => view_any_post::category,\n  target => null,\n  result => null,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-304302445 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post::category </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_post::category</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-304302445\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.063649, "xdebug_link": null}, {"message": "[\n  ability => view_any_post,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1545794500 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_post</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1545794500\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.065441, "xdebug_link": null}, {"message": "[\n  ability => view_any_regulation,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-487345565 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_regulation </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">view_any_regulation</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-487345565\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.070159, "xdebug_link": null}, {"message": "[\n  ability => view_any_setting,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1627975105 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627975105\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.072081, "xdebug_link": null}, {"message": "[\n  ability => view_any_userinternal,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1538004460 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_userinternal </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_userinternal</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538004460\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.084404, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1110268012 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1110268012\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.089779, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user_internal => 5,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1073481369 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073481369\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.090875, "xdebug_link": null}, {"message": "[\n  ability => page_Product,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1522269055 data-indent-pad=\"  \"><span class=sf-dump-note>page_Product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">page_Product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522269055\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.142653, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-712479450 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712479450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.148251, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-232867363 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-232867363\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.149505, "xdebug_link": null}, {"message": "[\n  ability => view_any_post::category,\n  target => null,\n  result => null,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-920249662 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post::category </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_post::category</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920249662\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.151733, "xdebug_link": null}, {"message": "[\n  ability => view_any_post,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1388746625 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_post</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1388746625\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.15336, "xdebug_link": null}, {"message": "[\n  ability => view_any_regulation,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1212536865 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_regulation </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">view_any_regulation</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212536865\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.156544, "xdebug_link": null}, {"message": "[\n  ability => view_any_setting,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1531102327 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1531102327\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.158921, "xdebug_link": null}, {"message": "[\n  ability => view_any_userinternal,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-526231475 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_userinternal </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_userinternal</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-526231475\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.170017, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1286546270 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286546270\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.17342, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user_internal => 5,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1910804111 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910804111\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.174401, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://dev.psp.com/internal/subscriptions", "action_name": "filament.internal.resources.subscriptions.index", "controller_action": "App\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions", "uri": "GET internal/subscriptions", "controller": "App\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions@render<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "internal/subscriptions", "file": "<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:internal, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "telescope": "<a href=\"https://dev.psp.com/_debugbar/telescope/9f6d29f2-c308-4333-8c08-13df2cbbd37d\" target=\"_blank\">View in Telescope</a>", "duration": "1.38s", "peak_memory": "14MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1695 characters\">remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5QMVd4eVAzYWVDZTJPbU1IOC9lVUE9PSIsInZhbHVlIjoiOXJ2dHFYRUd1WU1TRis0Njg2VHRKV3FlTUhPR3ZOZ0FMSzVTdG9SK2RWTCsvOEViMEFCbTdBdHRTc1h4WXZSb0lvU2x2MTBFODBCaFREVlBScW9zSkhjR2xURktSMjFaUW9sYUNFVXBkUkRLQmdrVkZtQzJIOXBFVlpCVnNTY1BvNWs0YWtwMGxKS3VNdTIvQldFZWdnPT0iLCJtYWMiOiI3ODdkN2ExYmExZjU5MzE2ODBmZTU4NmRhNmQxMDVjNTUxMWE3ODEyY2EzYWVkNTU0NWUwOTk4ZmI2YjczNDdjIiwidGFnIjoiIn0%3D; remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IitDNnNWWlM0T0JuVitIL2ptLzY3WWc9PSIsInZhbHVlIjoiYkRZRFJ5MlMxbFBXL2dqNzlzQmwvQW9HVk11N3hYS3IxMHZZMkZCWWhjWko3N1kvbDYvTGU2UDZ0NE1vbHRBUUwrdUFSam15akJlVjdvbVBGWXFLVFUxVVlBWUM2UkxNVmgyWGVZU2NnL21RMjJybUZuZzFOTFN1Z3RVRUNobCtiNjJmOEs4TWZ0NGM1bittb3ZaLzNFZFVvTTRycVM5RHBLaGsyUThNVzZqdFNiYnBldVV0TWNZLzRocjcxUGZ0UUhBaG5RWXlIdEU0d3RnQUZMZVAwTldqMHJyYVBlZ0lrQlphOHNuWVpBQT0iLCJtYWMiOiI1ZjhkMDMzYzRiMzAyOWFmZWJhNThmYzYwODU3Yjc2N2NkYTg1OWJmZTkxNzUwYWVhMjg0ODQzODMxNDlkOTg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImU3WGpSVkx6UFRTSnpyQ0ZrRThwVEE9PSIsInZhbHVlIjoiK2o5amJtZzE2bXZ2RjBmZ0U0M0RiSUJaRjhSV1RTR2tqTHIvaFIvZlFFTkZEUzhxNzgrZ25vdnBHRCsvL2gxUDJGNTQrVzcwV3pBSU1zcTlkNUQ4QTFjM2orczc3aHBWMzJJY2NNdy9KSWhBY2pBaWlpamxkKzhPSktQTzhPN1UiLCJtYWMiOiIwZjBiYjllM2M5YjBiOWEyNzNhMWU4Nzk0YzZhMmM4ZDMzNmZkOTQzYjViNTJjNDIxMmE3ZTVmODE1NDhiM2ZmIiwidGFnIjoiIn0%3D; ceval_app_session=eyJpdiI6Ik5iU1RraGxXYlpXTDFWRHNWendyZ2c9PSIsInZhbHVlIjoiNUc4SEszejhQQmY3U3lwZzFlTkNhRmVuSjFjWE1JdWNqdGN4U2JWUkhFV05kMFZ2QTlaNkpiNExuSGR4RlZlTmhKM0lmb0RseE5rZDBYbVFKVVgzNUxFdzVSMVdmeXdpYzZ3NFZ3QllCeE5rNitWaTdrTVVmVlBKSjJuUVpJVUMiLCJtYWMiOiI2NzEwNWEyNzdlNDcwODU2NjlhZTgyNWQxMDNmMTI4OWE1YTU1NjY5NGIyN2E1ZjZlNmM0MGRiNmRiZTM4NjAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,id;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://dev.psp.com/internal/subscriptions</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">dev.psp.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1557182463 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">4|q5hZpWB7QSHiPq0hTFEb4e5IyIUPCzdE5GMeANTAcy4FtWS6lj04ZIiQL38G|</span>\"\n  \"<span class=sf-dump-key>remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">5|bQicjYEg6cF5oW38bBsKPo6sbK55hIXGXRCTgCFFQxCRgmArAxSeD8RHgEnv|$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>ceval_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TBncuK1Z7kHcGjnRHlb51hN4z6hEodSBkwEbeDjj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557182463\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2017924619 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 08:00:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6InAwckxsNW43S0x5Y1JOVUQ0bTBNTmc9PSIsInZhbHVlIjoid2MrNW1sclp6SWhiMENjZ25WSkdka0dHNGFGaWlnWm5GemhaajUyM3lrV3JxRTdUc1gybjVJOVpaMFNJS2crYjQ5R0hOWlhJYkdybXE4QUJBNXErSFpXbXZmeUNNYWMzNG11OEltRU5JU05JWFl6WDMvZG1hb25TMkZ4YWgvWFQiLCJtYWMiOiI0M2I2ODlhY2E2MDUzNWRiYzYxMzBjYjMzMjdmZTI4NzFmMWU4OGIwZjYwYjM2OGIzNjI2OWMzMjE0YTI0ODA0IiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 10:00:40 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"453 characters\">ceval_app_session=eyJpdiI6InkzOXNUME5BSkFNNzBIbTNSQ2FjVkE9PSIsInZhbHVlIjoiYjlKV2s0eEExYmpxZ0RrQUd0UVgycng4aFJwUnozcmkvcEFDbjExQ2wxSWIvUmtpMUhGRkg3Vno3TjNFamhDQW00K3NMck55aWh3R2JHOTQ4NlpNbDZnZHdXOHhDWUJ6bDZ0K1J5Uy8zQXNYZzNIYlpwc2lhU2ZqbEpBajIzaDUiLCJtYWMiOiJmN2M2NzRjMDJjNTczMDhlYTY0NGE3ZmFiZjZkMDhjMDgwZGQ1NTA2YjZkODdlNGE2NTkyYWI1MjAzYjI2ZGRiIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 10:00:40 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6InAwckxsNW43S0x5Y1JOVUQ0bTBNTmc9PSIsInZhbHVlIjoid2MrNW1sclp6SWhiMENjZ25WSkdka0dHNGFGaWlnWm5GemhaajUyM3lrV3JxRTdUc1gybjVJOVpaMFNJS2crYjQ5R0hOWlhJYkdybXE4QUJBNXErSFpXbXZmeUNNYWMzNG11OEltRU5JU05JWFl6WDMvZG1hb25TMkZ4YWgvWFQiLCJtYWMiOiI0M2I2ODlhY2E2MDUzNWRiYzYxMzBjYjMzMjdmZTI4NzFmMWU4OGIwZjYwYjM2OGIzNjI2OWMzMjE0YTI0ODA0IiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 10:00:40 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"425 characters\">ceval_app_session=eyJpdiI6InkzOXNUME5BSkFNNzBIbTNSQ2FjVkE9PSIsInZhbHVlIjoiYjlKV2s0eEExYmpxZ0RrQUd0UVgycng4aFJwUnozcmkvcEFDbjExQ2wxSWIvUmtpMUhGRkg3Vno3TjNFamhDQW00K3NMck55aWh3R2JHOTQ4NlpNbDZnZHdXOHhDWUJ6bDZ0K1J5Uy8zQXNYZzNIYlpwc2lhU2ZqbEpBajIzaDUiLCJtYWMiOiJmN2M2NzRjMDJjNTczMDhlYTY0NGE3ZmFiZjZkMDhjMDgwZGQ1NTA2YjZkODdlNGE2NTkyYWI1MjAzYjI2ZGRiIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 10:00:40 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017924619\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>password_hash_internal</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>ListOrders_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://dev.psp.com/internal/subscriptions</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>ListSubscriptions_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-28</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-02</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://dev.psp.com/internal/subscriptions", "action_name": "filament.internal.resources.subscriptions.index", "controller_action": "App\\Filament\\Internal\\Resources\\SubscriptionResource\\Pages\\ListSubscriptions"}, "badge": null}}