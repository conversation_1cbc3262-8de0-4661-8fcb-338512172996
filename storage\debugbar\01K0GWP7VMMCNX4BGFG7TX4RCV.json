{"__meta": {"id": "01K0GWP7VMMCNX4BGFG7TX4RCV", "datetime": "2025-07-19 15:26:56", "utime": **********.757031, "method": "GET", "uri": "/internal/invoices?activeTab=3", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752913614.95147, "end": **********.75708, "duration": 1.805610179901123, "duration_str": "1.81s", "measures": [{"label": "Booting", "start": 1752913614.95147, "relative_start": 0, "end": **********.303738, "relative_end": **********.303738, "duration": 0.****************, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.303765, "relative_start": 0.****************, "end": **********.757085, "relative_end": 5.0067901611328125e-06, "duration": 1.***************, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.821556, "relative_start": 0.****************, "end": **********.822633, "relative_end": **********.822633, "duration": 0.0010769367218017578, "duration_str": "1.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.060285, "relative_start": 1.****************, "end": **********.060285, "relative_end": **********.060285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.11622, "relative_start": 1.***************, "end": **********.11622, "relative_end": **********.11622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.17334, "relative_start": 1.2218701839447021, "end": **********.17334, "relative_end": **********.17334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.232841, "relative_start": 1.2813711166381836, "end": **********.232841, "relative_end": **********.232841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.293637, "relative_start": 1.3421671390533447, "end": **********.293637, "relative_end": **********.293637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.358987, "relative_start": 1.4075171947479248, "end": **********.358987, "relative_end": **********.358987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.418603, "relative_start": 1.4671330451965332, "end": **********.418603, "relative_end": **********.418603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": **********.428842, "relative_start": 1.477372169494629, "end": **********.428842, "relative_end": **********.428842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.492796, "relative_start": 1.5413260459899902, "end": **********.492796, "relative_end": **********.492796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e93d402ed1f3da8a07f4840136a03cb", "start": **********.59133, "relative_start": 1.6398601531982422, "end": **********.59133, "relative_end": **********.59133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.754229, "relative_start": 1.8027591705322266, "end": **********.754322, "relative_end": **********.754322, "duration": 9.298324584960938e-05, "duration_str": "93μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.756245, "relative_start": 1.8047749996185303, "end": **********.756417, "relative_end": **********.756417, "duration": 0.00017213821411132812, "duration_str": "172μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 10816560, "peak_usage_str": "10MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.18", "Environment": "local", "Debug Mode": "Enabled", "URL": "dev.psp.com", "Timezone": "Asia/Jakarta", "Locale": "en"}}, "views": {"count": 10, "nb_templates": 10, "templates": [{"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.06021, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.116176, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.173291, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.232809, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.293602, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.358925, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.418548, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "livewire::tailwind", "param_count": null, "params": [], "start": **********.428801, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.492769, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::5e93d402ed1f3da8a07f4840136a03cb", "param_count": null, "params": [], "start": **********.591235, "type": "blade", "hash": "bladeD:\\WebData\\www\\psp\\storage\\framework\\views/5e93d402ed1f3da8a07f4840136a03cb.blade.php__components::5e93d402ed1f3da8a07f4840136a03cb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fstorage%2Fframework%2Fviews%2F5e93d402ed1f3da8a07f4840136a03cb.blade.php&line=1", "ajax": false, "filename": "5e93d402ed1f3da8a07f4840136a03cb.blade.php", "line": "?"}}]}, "queries": {"count": 18, "nb_statements": 18, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07219, "accumulated_duration_str": "72.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 5 and `user_type` = 'admin' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.829108, "duration": 0.00504, "duration_str": "5.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "psp_dev", "explain": null, "start_percent": 0, "width_percent": 6.982}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'pending' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.870831, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 6.982, "width_percent": 4.197}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'processing' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["processing"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.877976, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 11.179, "width_percent": 4.46}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'paid' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.884332, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 15.639, "width_percent": 4.405}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'failed' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["failed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.8904111, "duration": 0.00292, "duration_str": "2.92ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 20.044, "width_percent": 4.045}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'expired' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["expired"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.897448, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 24.089, "width_percent": 4.89}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'cancelled' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["cancelled"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.904449, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 28.979, "width_percent": 4.585}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'refunded' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["refunded"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Concerns/HasTabs.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Concerns\\HasTabs.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}], "start": **********.911576, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "ListInvoices.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Pages/ListInvoices.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FPages%2FListInvoices.php&line=44", "ajax": false, "filename": "ListInvoices.php", "line": "44"}, "connection": "psp_dev", "explain": null, "start_percent": 33.564, "width_percent": 3.796}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'paid' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.952274, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "psp_dev", "explain": null, "start_percent": 37.36, "width_percent": 4.059}, {"sql": "select * from `invoices` where `payment_status` = 'paid' and `invoices`.`deleted_at` is null order by `invoice_date` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.958076, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 41.418, "width_percent": 4.807}, {"sql": "select * from `users` where `users`.`id` in (4) and `user_type` = 'user' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["user"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.966861, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "psp_dev", "explain": null, "start_percent": 46.225, "width_percent": 3.893}, {"sql": "select * from `subscriptions` where `subscriptions`.`id` = 83 and `subscriptions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [83], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "D:\\WebData\\www\\psp\\app\\Models\\Invoice.php", "line": 135}, {"index": 31, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 80}, {"index": 32, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeCopied.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeCopied.php", "line": 18}, {"index": 33, "namespace": "view", "name": "filament-tables::index", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 2142}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.022867, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "Invoice.php:135", "source": {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "D:\\WebData\\www\\psp\\app\\Models\\Invoice.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FInvoice.php&line=135", "ajax": false, "filename": "Invoice.php", "line": "135"}, "connection": "psp_dev", "explain": null, "start_percent": 50.118, "width_percent": 5.679}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (5) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\UserInternal'", "type": "query", "params": [], "bindings": ["App\\Models\\UserInternal"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.502821, "duration": 0.009349999999999999, "duration_str": "9.35ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "psp_dev", "explain": null, "start_percent": 55.797, "width_percent": 12.952}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (5) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\UserInternal'", "type": "query", "params": [], "bindings": ["App\\Models\\UserInternal"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.5160499, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\WebData\\www\\psp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "psp_dev", "explain": null, "start_percent": 68.749, "width_percent": 4.904}, {"sql": "select count(*) as aggregate from `tickets` where `status` = 'open' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.5511441, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "TicketResource.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FTicketResource.php&line=33", "ajax": false, "filename": "TicketResource.php", "line": "33"}, "connection": "psp_dev", "explain": null, "start_percent": 73.653, "width_percent": 4.557}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.570446, "duration": 0.00835, "duration_str": "8.35ms", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "psp_dev", "explain": null, "start_percent": 78.21, "width_percent": 11.567}, {"sql": "select count(*) as aggregate from `tickets` where `status` = 'open' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.649982, "duration": 0.00315, "duration_str": "3.15ms", "memory": 0, "memory_str": null, "filename": "TicketResource.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/TicketResource.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\TicketResource.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FTicketResource.php&line=33", "ajax": false, "filename": "TicketResource.php", "line": "33"}, "connection": "psp_dev", "explain": null, "start_percent": 89.777, "width_percent": 4.363}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.666349, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "RoleResource.php:206", "source": {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Resources/RoleResource.php", "file": "D:\\WebData\\www\\psp\\vendor\\bezhansalleh\\filament-shield\\src\\Resources\\RoleResource.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FResources%2FRoleResource.php&line=206", "ajax": false, "filename": "RoleResource.php", "line": "206"}, "connection": "psp_dev", "explain": null, "start_percent": 94.14, "width_percent": 5.86}]}, "models": {"data": {"App\\Models\\Invoice": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "App\\Models\\UserInternal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserInternal.php&line=1", "ajax": false, "filename": "UserInternal.php", "line": "?"}}, "App\\Models\\UserMember": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserMember.php&line=1", "ajax": false, "filename": "UserMember.php", "line": "?"}}, "App\\Models\\Subscription": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FSubscription.php&line=1", "ajax": false, "filename": "Subscription.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 11, "is_counter": true}, "livewire": {"data": {"app.filament.internal.resources.invoice-resource.pages.list-invoices #aHIkFqOps2H9DQobcEVR": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => null\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => \"3\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.internal.resources.invoice-resource.pages.list-invoices\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices\"\n  \"id\" => \"aHIkFqOps2H9DQobcEVR\"\n]", "filament.livewire.notifications #760FZqHIrOcMVdDFwhxb": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#5436\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"760FZqHIrOcMVdDFwhxb\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 20, "messages": [{"message": "[\n  ability => page_Product,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-86217042 data-indent-pad=\"  \"><span class=sf-dump-note>page_Product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">page_Product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86217042\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.522804, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1990471438 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1990471438\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.532519, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1112906963 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1112906963\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.533782, "xdebug_link": null}, {"message": "[\n  ability => view_any_post::category,\n  target => null,\n  result => null,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1797240050 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post::category </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_post::category</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1797240050\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.535961, "xdebug_link": null}, {"message": "[\n  ability => view_any_post,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-987371515 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_post</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987371515\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.536951, "xdebug_link": null}, {"message": "[\n  ability => view_any_regulation,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-187211456 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_regulation </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">view_any_regulation</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-187211456\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.540489, "xdebug_link": null}, {"message": "[\n  ability => view_any_setting,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1175940229 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175940229\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543083, "xdebug_link": null}, {"message": "[\n  ability => view_any_userinternal,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-193993608 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_userinternal </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_userinternal</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-193993608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.560396, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1111220471 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1111220471\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.566769, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user_internal => 5,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-89116611 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89116611\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.567993, "xdebug_link": null}, {"message": "[\n  ability => page_Product,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1397621007 data-indent-pad=\"  \"><span class=sf-dump-note>page_Product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">page_Product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1397621007\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.62359, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-772444101 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-772444101\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.63255, "xdebug_link": null}, {"message": "[\n  ability => view_any_page,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1844586713 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844586713\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.633922, "xdebug_link": null}, {"message": "[\n  ability => view_any_post::category,\n  target => null,\n  result => null,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1645848504 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post::category </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_post::category</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1645848504\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.636106, "xdebug_link": null}, {"message": "[\n  ability => view_any_post,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1099960653 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_post </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_post</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099960653\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.637668, "xdebug_link": null}, {"message": "[\n  ability => view_any_regulation,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1015358487 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_regulation </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">view_any_regulation</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1015358487\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.6427, "xdebug_link": null}, {"message": "[\n  ability => view_any_setting,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-257030290 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257030290\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.644828, "xdebug_link": null}, {"message": "[\n  ability => view_any_userinternal,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1253331346 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_userinternal </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">view_any_userinternal</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1253331346\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.658163, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user_internal => 5,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1648649671 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1648649671\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.662614, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user_internal => 5,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-831793643 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user_internal</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831793643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.663661, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://dev.psp.com/internal/invoices?activeTab=3", "action_name": "filament.internal.resources.invoices.index", "controller_action": "App\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices", "uri": "GET internal/invoices", "controller": "App\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices@render<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "internal/invoices", "file": "<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:internal, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "telescope": "<a href=\"https://dev.psp.com/_debugbar/telescope/9f6d3358-4ba6-4c5d-91c5-16fcfc99ecbd\" target=\"_blank\">View in Telescope</a>", "duration": "1.81s", "peak_memory": "16MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>activeTab</span>\" => \"<span class=sf-dump-str>3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2053005208 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1695 characters\">remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5QMVd4eVAzYWVDZTJPbU1IOC9lVUE9PSIsInZhbHVlIjoiOXJ2dHFYRUd1WU1TRis0Njg2VHRKV3FlTUhPR3ZOZ0FMSzVTdG9SK2RWTCsvOEViMEFCbTdBdHRTc1h4WXZSb0lvU2x2MTBFODBCaFREVlBScW9zSkhjR2xURktSMjFaUW9sYUNFVXBkUkRLQmdrVkZtQzJIOXBFVlpCVnNTY1BvNWs0YWtwMGxKS3VNdTIvQldFZWdnPT0iLCJtYWMiOiI3ODdkN2ExYmExZjU5MzE2ODBmZTU4NmRhNmQxMDVjNTUxMWE3ODEyY2EzYWVkNTU0NWUwOTk4ZmI2YjczNDdjIiwidGFnIjoiIn0%3D; remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IitDNnNWWlM0T0JuVitIL2ptLzY3WWc9PSIsInZhbHVlIjoiYkRZRFJ5MlMxbFBXL2dqNzlzQmwvQW9HVk11N3hYS3IxMHZZMkZCWWhjWko3N1kvbDYvTGU2UDZ0NE1vbHRBUUwrdUFSam15akJlVjdvbVBGWXFLVFUxVVlBWUM2UkxNVmgyWGVZU2NnL21RMjJybUZuZzFOTFN1Z3RVRUNobCtiNjJmOEs4TWZ0NGM1bittb3ZaLzNFZFVvTTRycVM5RHBLaGsyUThNVzZqdFNiYnBldVV0TWNZLzRocjcxUGZ0UUhBaG5RWXlIdEU0d3RnQUZMZVAwTldqMHJyYVBlZ0lrQlphOHNuWVpBQT0iLCJtYWMiOiI1ZjhkMDMzYzRiMzAyOWFmZWJhNThmYzYwODU3Yjc2N2NkYTg1OWJmZTkxNzUwYWVhMjg0ODQzODMxNDlkOTg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IklmS3VKTmJHODZDbkc5Q09IY3h4N3c9PSIsInZhbHVlIjoicUZmN1o2bUFzUi9mS0FaeE5lZlVzcFEva0FmWHpBMVJ2YnR2azEvY2VYZDVWd21OYTNuSUE3ZWF4ZnFPS1RLZmF2UDBTU2xNNEUvM0hId0QveFhITC9XWEhtV3U2RWE3c2hvTzJSMERPQjNldVdaVUhxR3B6TzlsOEN4anJzVnciLCJtYWMiOiI4NTQ3MTZlNTY0ZmNhMGMxNThmMThjNjMwNDYyM2FkYjE2MzNkODE5ZmYxZTQ3MmM0OWUxZGM5Njg4ZDhhZWVhIiwidGFnIjoiIn0%3D; ceval_app_session=eyJpdiI6Im0vaW1DSGN5SENaM0NjYTF3NEpWRlE9PSIsInZhbHVlIjoiQ0M3T3kvWE9uVkNSTFBKd1pwM1RzUmJncXBBdW41b2E1SU56eTFQYnVUVFVpbWthclhGQ09sN0REeFA3QmJDUnpXaWV6UHNzRWxEbkpwWGFjVkZHY1lUMmJlWGlxQ1JHR05ndmRObms1dnlWUVJDWW5yWTJhYm5rZ0VROGhpMmkiLCJtYWMiOiI5Yzg5NGJkM2M5OTFkOWY5OWRlODY4MWI3NGExYWI4OWExMmIzZmYzZmRjNmU5NmY0YTY4YzQ2MjBhNTFlZWY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,id;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">https://dev.psp.com/internal/invoices?activeTab=3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">dev.psp.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053005208\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-895483083 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">4|q5hZpWB7QSHiPq0hTFEb4e5IyIUPCzdE5GMeANTAcy4FtWS6lj04ZIiQL38G|</span>\"\n  \"<span class=sf-dump-key>remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">5|bQicjYEg6cF5oW38bBsKPo6sbK55hIXGXRCTgCFFQxCRgmArAxSeD8RHgEnv|$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>ceval_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TBncuK1Z7kHcGjnRHlb51hN4z6hEodSBkwEbeDjj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-895483083\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2134282772 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 08:26:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IkhQK0swUkxoTVNTbjloNlljakdUMlE9PSIsInZhbHVlIjoiVEMyc3FobGJINXQxeHhsTDFxbVV6alJCdUtZR05WZjI3dHNJREdYbndqdytNLzc1Ykd1SHIyaUIrMWVXb3cxeHBNb1ExQmJVK0NwQmE2NHRnUGQ3M2UzemhuRmhGWGpzaDVKT0ZKVUl1OEJLOFJFOURBL1Jqa05SY1RWVENwenQiLCJtYWMiOiJiNDgzYzI4ZmRlMjBkYzIzMWZiOGVjNGI3MGI0YmM4MDZjYWExZDU1N2FjZGNlZWQ3ZjFmNDZhNzUyNjJiNzk0IiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 10:26:56 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"453 characters\">ceval_app_session=eyJpdiI6InVpNEhJRG5PUGUwRWNxekNjSlFvUHc9PSIsInZhbHVlIjoicGoyVlFmVXhIYjdzSWdTMnAvOElWRzJ3VEZ3YUdpUG9yYmE3UHRaY1M2QVJKZ3BJdTZJTjZpWVZCWmVWbC9QNHZvcjFOVEx1UGNzR3FIeFlFUExNbnpqRnpTNmFGUGVFd1BXWTQ4dXA1MkdER01CVWVQa3B4R2VFaDlOblBieCsiLCJtYWMiOiJmMDc1MWE4ZDQ1MTUxZjVhMWRlOTIxZDQ2OTA5MmUxY2I2NWE5ODg3YmJkNGE2NTQ2NGUyNTlkYTc0NDkzM2NlIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 10:26:56 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IkhQK0swUkxoTVNTbjloNlljakdUMlE9PSIsInZhbHVlIjoiVEMyc3FobGJINXQxeHhsTDFxbVV6alJCdUtZR05WZjI3dHNJREdYbndqdytNLzc1Ykd1SHIyaUIrMWVXb3cxeHBNb1ExQmJVK0NwQmE2NHRnUGQ3M2UzemhuRmhGWGpzaDVKT0ZKVUl1OEJLOFJFOURBL1Jqa05SY1RWVENwenQiLCJtYWMiOiJiNDgzYzI4ZmRlMjBkYzIzMWZiOGVjNGI3MGI0YmM4MDZjYWExZDU1N2FjZGNlZWQ3ZjFmNDZhNzUyNjJiNzk0IiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 10:26:56 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"425 characters\">ceval_app_session=eyJpdiI6InVpNEhJRG5PUGUwRWNxekNjSlFvUHc9PSIsInZhbHVlIjoicGoyVlFmVXhIYjdzSWdTMnAvOElWRzJ3VEZ3YUdpUG9yYmE3UHRaY1M2QVJKZ3BJdTZJTjZpWVZCWmVWbC9QNHZvcjFOVEx1UGNzR3FIeFlFUExNbnpqRnpTNmFGUGVFd1BXWTQ4dXA1MkdER01CVWVQa3B4R2VFaDlOblBieCsiLCJtYWMiOiJmMDc1MWE4ZDQ1MTUxZjVhMWRlOTIxZDQ2OTA5MmUxY2I2NWE5ODg3YmJkNGE2NTQ2NGUyNTlkYTc0NDkzM2NlIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 10:26:56 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2134282772\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1864692908 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>password_hash_internal</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>ListOrders_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">https://dev.psp.com/internal/invoices?activeTab=3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>ListSubscriptions_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-28</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-12-31</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864692908\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://dev.psp.com/internal/invoices?activeTab=3", "action_name": "filament.internal.resources.invoices.index", "controller_action": "App\\Filament\\Internal\\Resources\\InvoiceResource\\Pages\\ListInvoices"}, "badge": null}}