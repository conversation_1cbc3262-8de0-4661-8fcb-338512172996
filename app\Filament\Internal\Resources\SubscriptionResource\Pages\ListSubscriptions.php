<?php

namespace App\Filament\Internal\Resources\SubscriptionResource\Pages;

use App\Models\Plan;
use App\Enum\PaymentStatusEnum;
use Filament\Resources\Components\Tab;
use Filament\Forms\Components\DatePicker;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Filament\Pages\Dashboard\Actions\FilterAction;
use Filament\Pages\Dashboard\Concerns\HasFiltersAction;
use App\Filament\Internal\Resources\SubscriptionResource;
use App\Filament\Internal\Resources\SubscriptionResource\Widgets\GrowthChart;
use App\Filament\Internal\Resources\SubscriptionResource\Widgets\StatsOverview;

class ListSubscriptions extends ListRecords
{
    use HasFiltersAction;

    protected static string $resource = SubscriptionResource::class;


    protected function getHeaderActions(): array
    {
        return [
            //Actions\CreateAction::make(),
            FilterAction::make()
                ->label('Filter Stats')
                ->form(
                    [
                        DatePicker::make('startDate')
                            ->required(),
                        DatePicker::make('endDate')
                            ->required()
                            ->after('startDate'),
                        // ...
                    ]
                ),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            StatsOverview::make([
                'filters' => $this->filters,
            ]),
            GrowthChart::make([
                'filters' => $this->filters,
            ]),
        ];
    }

    public function getTabs(): array
    {
        $plan = Plan::get();

        $result = [
            Tab::make('All'),
            Tab::make('Paid')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('payment_status', PaymentStatusEnum::PAID->value)),
            Tab::make('Pending')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('payment_status', PaymentStatusEnum::PENDING->value)),
        ];


        return $result;
    }
}
