---
applyTo: "**"
---

# Documentation String Standards for PSP Project

## Class Documentation

Always document classes using PHPDoc blocks with the following format:

```php
/**
 * Handles payment processing through Midtrans gateway.
 *
 * This class manages all payment-related operations including:
 * - Payment URL generation
 * - Webhook processing
 * - Status updates
 * - Payment notifications
 *
 * @package App\Aris\Payments
 */
class PaymentManager
{
```

## Property Documentation

Document properties with type information and description:

```php
/**
 * The status of the payment.
 *
 * @var PaymentStatusEnum
 */
protected PaymentStatusEnum $status;

/**
 * Configuration settings for the payment gateway.
 *
 * @var array{
 *   server_key: string,
 *   client_key: string,
 *   is_production: bool
 * }
 */
protected array $config;
```

## Method Documentation

Document methods with:

-   Description
-   Parameters with types
-   Return type
-   Thrown exceptions
-   Example usage when complex

```php
/**
 * Generate a payment URL for the given invoice.
 *
 * @param Invoice $invoice The invoice to generate payment URL for
 * @param string $provider Payment provider name (default: 'midtrans')
 * @param array $additionalData Additional data for the payment provider
 * @param array $discount Discount information if applicable
 * @param array $tax Tax information if applicable
 * @throws \Exception When payment URL generation fails
 * @return string|null Payment URL or null on failure
 *
 * @example
 * $paymentUrl = $manager->generatePaymentUrl(
 *     invoice: $invoice,
 *     provider: 'midtrans',
 *     additionalData: [],
 *     discount: ['amount' => 1000],
 *     tax: ['amount' => 1100]
 * );
 */
public function generatePaymentUrl(
    Invoice $invoice,
    string $provider = 'midtrans',
    array $additionalData = [],
    array $discount = [],
    array $tax = []
): ?string
```

## Interface Documentation

Document interfaces with implementation details:

```php
/**
 * Defines payment gateway provider functionality.
 *
 * Implementations must handle:
 * - Payment URL generation
 * - Webhook processing
 * - Status updates
 *
 * @see \App\Aris\Payments\Midtrans For reference implementation
 */
interface PaymentGatewayInterface
{
```

## Trait Documentation

Document traits with usage instructions:

```php
/**
 * Provides file handling functionality for models.
 *
 * Use this trait in models that need to:
 * - Handle file uploads
 * - Manage file storage
 * - Clean up files on deletion
 *
 * @mixin \Illuminate\Database\Eloquent\Model
 */
trait HandlesFiles
{
```

## Special Cases

### Event Handlers

Document event handlers with event data structure:

```php
/**
 * Handle payment completion event.
 *
 * @param PaymentCompleted $event Event data containing:
 *     - string $transactionId
 *     - float $amount
 *     - \DateTime $paidAt
 */
public function handlePaymentCompleted(PaymentCompleted $event): void
```

### Service Provider Methods

Document provider methods with registration details:

```php
/**
 * Register payment services.
 *
 * Binds:
 * - PaymentGatewayInterface to Midtrans
 * - PaymentManager as singleton
 */
public function register(): void
```

### Middleware

Document middleware with route usage:

````php
/**
 * Verify subscription status middleware.
 *
 * Use in routes requiring active subscription:
 * ```php
 * Route::middleware(['auth:member', 'verified.subscription'])
 * ```
 *
 * @param Request $request
 * @param Closure $next
 */
public function handle(Request $request, Closure $next)
````

## Best Practices

1. Always include parameter and return types in docblocks AND type hints
2. Document exceptions that may be thrown
3. Include usage examples for complex methods
4. Reference related classes and interfaces
5. Keep descriptions concise but complete
