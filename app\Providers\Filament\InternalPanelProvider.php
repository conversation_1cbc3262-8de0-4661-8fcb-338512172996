<?php

namespace App\Providers\Filament;

use Filament\Pages;
use Filament\Panel;
use App\Models\User;
use Filament\Widgets;
use App\Enum\UserTypeEnum;
use Filament\PanelProvider;
use App\Models\Notification;
use Filament\Navigation\MenuItem;
use Filament\Support\Colors\Color;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Filament\Support\Enums\Alignment;
use Filament\Http\Middleware\Authenticate;
use App\Filament\Internal\Pages\Auth\Login;
use Illuminate\Session\Middleware\StartSession;
use App\Notifications\InternalPanelNotification;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Notifications\Livewire\Notifications;
use App\Filament\Internal\Pages\Profile\ProfileView;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use BezhanSalleh\FilamentShield\Facades\FilamentShield;
use Filament\Http\Middleware\DisableBladeIconComponents;
use App\Filament\Internal\Pages\Auth\RequestPasswordReset;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Biostate\FilamentMenuBuilder\FilamentMenuBuilderPlugin;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;

/**
 * Provider class for configuring the internal admin panel.
 *
 * This class extends PanelProvider to set up and configure a Filament admin panel
 * specifically for internal use, including authentication, middleware, resources,
 * pages and widgets.
 */
class InternalPanelProvider extends PanelProvider
{

    public function boot(): void
    {
        FilamentShield::configurePermissionIdentifierUsing(
            fn($resource) => str($resource::getModel())
                ->afterLast('\\')
                ->replace('_', '')
                ->lower()
                ->toString()
        );

        Gate::guessPolicyNamesUsing(function (string $modelClass) {
            return str_replace('Models', 'Policies', $modelClass) . 'Policy';
        });
    }

    /**
     * Configure and return the panel instance.
     *
     * @param Panel $panel The panel instance to configure
     *
     * @return Panel The configured panel instance
     */
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('internal')
            ->path('internal')
            ->authGuard('internal')
            ->colors(
                [
                    'primary' => Color::Amber,
                ]
            )
            //->spa()
            ->userMenuItems([
                'profile' => MenuItem::make()->label('Edit Profile')
                    ->icon('heroicon-o-user-circle')
                    ->url('/internal/profile-view'),
                //->url(route('filament.internal.pages.profile-view', absolute: true)),
                // ...
            ])
            ->broadcasting(false)
            ->unsavedChangesAlerts()
            ->databaseNotifications()
            //->databaseNotificationsPolling('5s')
            ->databaseTransactions()
            ->brandName(env("BRAND_NAME"))
            ->brandLogo(asset('assets/logo.jpg'))
            ->favicon(asset('assets/favicon.jpg'))
            ->brandLogoHeight('3rem')
            //->maxContentWidth('full')
            ->profile(isSimple: true)
            ->passwordReset(RequestPasswordReset::class)
            ->spa()
            ->colors(
                [
                    'primary' => Color::Violet,
                    'success' => Color::Green,
                    'danger' => Color::Rose,
                    'warning' => Color::Orange,
                    'blue' => Color::Blue,
                    'gray' => Color::Gray,
                    'indigo' => Color::Indigo,
                    'pink' => Color::Pink,
                    'purple' => Color::Purple,
                    'red' => Color::Red,
                    'teal' => Color::Teal,
                    'yellow' => Color::Yellow,
                    'green' => Color::Green,
                    'orange' => Color::Orange,
                ]
            )
            ->navigationGroups(
                [
                    'Ordering',
                    'Products',
                    'Customers',
                    'Website',
                    'Pengguna',
                    'Settings',
                    'Scrap'
                ]
            )
            ->viteTheme('resources/css/filament/internal/theme.css')
            ->login(Login::class)
            // TODO: Dont Forget to disable registration for production
            ->registration(false)
            ->discoverResources(in: app_path('Filament/Internal/Resources'), for: 'App\\Filament\\Internal\\Resources')
            ->discoverPages(in: app_path('Filament/Internal/Pages'), for: 'App\\Filament\\Internal\\Pages')
            ->discoverClusters(in: app_path('Filament/Internal/Clusters'), for: 'App\\Filament\\Internal\\Clusters')
            ->pages(
                [
                    Pages\Dashboard::class,
                ]
            )
            ->discoverWidgets(in: app_path('Filament/Internal/Widgets'), for: 'App\\Filament\\Internal\\Widgets')
            ->widgets(
                [
                    Widgets\AccountWidget::class,
                    //  Widgets\FilamentInfoWidget::class,
                ]
            )
            ->middleware(
                [
                    EncryptCookies::class,
                    AddQueuedCookiesToResponse::class,
                    StartSession::class,
                    AuthenticateSession::class,
                    ShareErrorsFromSession::class,
                    VerifyCsrfToken::class,
                    SubstituteBindings::class,
                    DisableBladeIconComponents::class,
                    DispatchServingFilamentEvent::class,
                ]
            )
            ->authMiddleware(
                [
                    Authenticate::class,
                ]
            )
            ->plugins(
                [
                    //FilamentMenuBuilderPlugin::make(),

                    \BezhanSalleh\FilamentShield\FilamentShieldPlugin::make()
                        ->gridColumns([
                            'default' => 1,
                            'sm' => 2,
                            'lg' => 3
                        ])
                        ->sectionColumnSpan(1)
                        ->checkboxListColumns([
                            'default' => 1,
                            'sm' => 2,
                            'lg' => 4,
                        ])
                        ->resourceCheckboxListColumns([
                            'default' => 1,
                            'sm' => 2,
                        ]),
                ]
            );
    }
}
