{"__meta": {"id": "01K0GWNV3YWP09PB42BAJ45FTB", "datetime": "2025-07-19 15:26:43", "utime": **********.710181, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752913602.8523, "end": **********.7102, "duration": 0.8579001426696777, "duration_str": "858ms", "measures": [{"label": "Booting", "start": 1752913602.8523, "relative_start": 0, "end": **********.160869, "relative_end": **********.160869, "duration": 0.*****************, "duration_str": "309ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.160889, "relative_start": 0.*****************, "end": **********.710203, "relative_end": 2.86102294921875e-06, "duration": 0.***************, "duration_str": "549ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.644703, "relative_start": 0.***************, "end": **********.645121, "relative_end": **********.645121, "duration": 0.0004181861877441406, "duration_str": "418μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.709473, "relative_start": 0.***************, "end": **********.709809, "relative_end": **********.709809, "duration": 0.0003361701965332031, "duration_str": "336μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8402312, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.7", "PHP Version": "8.2.18", "Environment": "local", "Debug Mode": "Enabled", "URL": "dev.psp.com", "Timezone": "Asia/Jakarta", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01983, "accumulated_duration_str": "19.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 5 and `user_type` = 'admin' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.652764, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\WebData\\www\\psp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "psp_dev", "explain": null, "start_percent": 0, "width_percent": 16.641}, {"sql": "select sum(`total_amount`) as aggregate from `invoices` where `payment_status` = 'paid' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 43}, {"index": 17, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.666648, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "PaidStatsOverview.php:43", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FWidgets%2FPaidStatsOverview.php&line=43", "ajax": false, "filename": "PaidStatsOverview.php", "line": "43"}, "connection": "psp_dev", "explain": null, "start_percent": 16.641, "width_percent": 12.708}, {"sql": "select count(*) as aggregate from `invoices` where `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 48}, {"index": 20, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 23, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.671385, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "PaidStatsOverview.php:48", "source": {"index": 19, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FWidgets%2FPaidStatsOverview.php&line=48", "ajax": false, "filename": "PaidStatsOverview.php", "line": "48"}, "connection": "psp_dev", "explain": null, "start_percent": 29.349, "width_percent": 12.103}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'paid' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 53}, {"index": 17, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.674988, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "PaidStatsOverview.php:53", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/PaidStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FWidgets%2FPaidStatsOverview.php&line=53", "ajax": false, "filename": "PaidStatsOverview.php", "line": "53"}, "connection": "psp_dev", "explain": null, "start_percent": 41.452, "width_percent": 8.22}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'pending' and `due_date` > '2025-07-19 15:26:43' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending", "2025-07-19 15:26:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/OverdueStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\OverdueStatsOverview.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.688153, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "OverdueStatsOverview.php:24", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/OverdueStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\OverdueStatsOverview.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FWidgets%2FOverdueStatsOverview.php&line=24", "ajax": false, "filename": "OverdueStatsOverview.php", "line": "24"}, "connection": "psp_dev", "explain": null, "start_percent": 49.672, "width_percent": 17.347}, {"sql": "select count(*) as aggregate from `invoices` where `payment_status` = 'pending' and `due_date` <= '2025-07-19 15:26:43' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending", "2025-07-19 15:26:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/OverdueStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\OverdueStatsOverview.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.693864, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "OverdueStatsOverview.php:25", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/OverdueStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\OverdueStatsOverview.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FWidgets%2FOverdueStatsOverview.php&line=25", "ajax": false, "filename": "OverdueStatsOverview.php", "line": "25"}, "connection": "psp_dev", "explain": null, "start_percent": 67.02, "width_percent": 11.447}, {"sql": "select sum(`total_amount`) as aggregate from `invoices` where `payment_status` = 'pending' and `due_date` > '2025-07-19 15:26:43' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending", "2025-07-19 15:26:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/OverdueStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\OverdueStatsOverview.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.698675, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "OverdueStatsOverview.php:27", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/OverdueStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\OverdueStatsOverview.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FWidgets%2FOverdueStatsOverview.php&line=27", "ajax": false, "filename": "OverdueStatsOverview.php", "line": "27"}, "connection": "psp_dev", "explain": null, "start_percent": 78.467, "width_percent": 11.851}, {"sql": "select sum(`total_amount`) as aggregate from `invoices` where `payment_status` = 'pending' and `due_date` <= '2025-07-19 15:26:43' and `invoices`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending", "2025-07-19 15:26:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/OverdueStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\OverdueStatsOverview.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "D:\\WebData\\www\\psp\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\WebData\\www\\psp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.702186, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "OverdueStatsOverview.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Internal/Resources/InvoiceResource/Widgets/OverdueStatsOverview.php", "file": "D:\\WebData\\www\\psp\\app\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\OverdueStatsOverview.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FFilament%2FInternal%2FResources%2FInvoiceResource%2FWidgets%2FOverdueStatsOverview.php&line=28", "ajax": false, "filename": "OverdueStatsOverview.php", "line": "28"}, "connection": "psp_dev", "explain": null, "start_percent": 90.318, "width_percent": 9.682}]}, "models": {"data": {"App\\Models\\UserInternal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fapp%2FModels%2FUserInternal.php&line=1", "ajax": false, "filename": "UserInternal.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"app.filament.internal.resources.invoice-resource.widgets.paid-stats-overview #Jau0iFk2dd6fSHdSavvM": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.internal.resources.invoice-resource.widgets.paid-stats-overview\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\PaidStatsOverview\"\n  \"id\" => \"Jau0iFk2dd6fSHdSavvM\"\n]", "app.filament.internal.resources.invoice-resource.widgets.overdue-stats-overview #TTyGY2yWXbKE1JmVLeMz": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.internal.resources.invoice-resource.widgets.overdue-stats-overview\"\n  \"component\" => \"App\\Filament\\Internal\\Resources\\InvoiceResource\\Widgets\\OverdueStatsOverview\"\n  \"id\" => \"TTyGY2yWXbKE1JmVLeMz\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://dev.psp.com/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FWebData%2Fwww%2Fpsp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "telescope": "<a href=\"https://dev.psp.com/_debugbar/telescope/9f6d3344-636c-40cd-8f0c-fc0d7c499c14\" target=\"_blank\">View in Telescope</a>", "duration": "860ms", "peak_memory": "10MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1659162969 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1659162969\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1405084790 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"320 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;Jau0iFk2dd6fSHdSavvM&quot;,&quot;name&quot;:&quot;app.filament.internal.resources.invoice-resource.widgets.paid-stats-overview&quot;,&quot;path&quot;:&quot;internal\\/invoices&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;7c53ec2c52d9966a3b66cf3e6e8f8d709e9da4c4f8e4381b34d8b09397e55e03&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"323 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;TTyGY2yWXbKE1JmVLeMz&quot;,&quot;name&quot;:&quot;app.filament.internal.resources.invoice-resource.widgets.overdue-stats-overview&quot;,&quot;path&quot;:&quot;internal\\/invoices&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;b123b08a790d02270d61414335b523136b4b019da6174b316433f9dbe9474af3&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405084790\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-524673367 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1695 characters\">remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5QMVd4eVAzYWVDZTJPbU1IOC9lVUE9PSIsInZhbHVlIjoiOXJ2dHFYRUd1WU1TRis0Njg2VHRKV3FlTUhPR3ZOZ0FMSzVTdG9SK2RWTCsvOEViMEFCbTdBdHRTc1h4WXZSb0lvU2x2MTBFODBCaFREVlBScW9zSkhjR2xURktSMjFaUW9sYUNFVXBkUkRLQmdrVkZtQzJIOXBFVlpCVnNTY1BvNWs0YWtwMGxKS3VNdTIvQldFZWdnPT0iLCJtYWMiOiI3ODdkN2ExYmExZjU5MzE2ODBmZTU4NmRhNmQxMDVjNTUxMWE3ODEyY2EzYWVkNTU0NWUwOTk4ZmI2YjczNDdjIiwidGFnIjoiIn0%3D; remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IitDNnNWWlM0T0JuVitIL2ptLzY3WWc9PSIsInZhbHVlIjoiYkRZRFJ5MlMxbFBXL2dqNzlzQmwvQW9HVk11N3hYS3IxMHZZMkZCWWhjWko3N1kvbDYvTGU2UDZ0NE1vbHRBUUwrdUFSam15akJlVjdvbVBGWXFLVFUxVVlBWUM2UkxNVmgyWGVZU2NnL21RMjJybUZuZzFOTFN1Z3RVRUNobCtiNjJmOEs4TWZ0NGM1bittb3ZaLzNFZFVvTTRycVM5RHBLaGsyUThNVzZqdFNiYnBldVV0TWNZLzRocjcxUGZ0UUhBaG5RWXlIdEU0d3RnQUZMZVAwTldqMHJyYVBlZ0lrQlphOHNuWVpBQT0iLCJtYWMiOiI1ZjhkMDMzYzRiMzAyOWFmZWJhNThmYzYwODU3Yjc2N2NkYTg1OWJmZTkxNzUwYWVhMjg0ODQzODMxNDlkOTg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ii9yQVcyMHFJcTh3ZURKVGIrVnlaOFE9PSIsInZhbHVlIjoiWnAwNVRVOFdlNjBNWk5XRmlqT1ZmUGxaK2l4QVR4NUJmREo1UTlqOHN1NkRzQ0lmdUI1RVBQMm41TDhaQ3RQZ2xiTDljUTBYUHpmYVJZMDh4ZEpFVHU1eUxFOGM2eHE1K0Mvc1grZXVjTGtFcFpaOTU1SWhhMU1OblEvS1NHMVoiLCJtYWMiOiI3NTUwMzU3M2EyNThhMGVlNmIzOWY0YmUxZDRmM2Y5ZDE1MTQzNWRjZTE5MjZhM2RlNTVlMWI5ZjkwNTMyNjcxIiwidGFnIjoiIn0%3D; ceval_app_session=eyJpdiI6ImJ3anhNMUFIa2paOGptdWtBNU1IK1E9PSIsInZhbHVlIjoicm5Rdi9DSmhpR0FzKysvMzBGRGFGUVJnTVIyZjIwVnZ5eklIUEFRcjQ3VTdwZEpMWnFzZmswRWFHdzFsY3BNRVpKWWVyWVpPU3ZBUy8rcnZTWWE1dHpMUGhYWm1sUlUxdEtNaHBBWUxDRUtzaWVVNG9VRTM4OEFiWGNoUlcyZysiLCJtYWMiOiIxYWRkZGRjNDYyZTI5OWIxMWZmYjA0YWM2ZGFmZjJiN2NhNjdiMzMwZWFjNWEwMzBkYzExNGY5MWY0Yzk0NzZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,id;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">https://dev.psp.com/internal/invoices?activeTab=3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://dev.psp.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">865</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">dev.psp.com</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-524673367\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1184334272 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_member_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">4|q5hZpWB7QSHiPq0hTFEb4e5IyIUPCzdE5GMeANTAcy4FtWS6lj04ZIiQL38G|</span>\"\n  \"<span class=sf-dump-key>remember_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">5|bQicjYEg6cF5oW38bBsKPo6sbK55hIXGXRCTgCFFQxCRgmArAxSeD8RHgEnv|$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>ceval_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TBncuK1Z7kHcGjnRHlb51hN4z6hEodSBkwEbeDjj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1184334272\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1450821128 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 08:26:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1450821128\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1261610756 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xzYCQRTzbXu82uGPdvR58dF3PSLgx0KBWsfy4quJ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_internal_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>password_hash_internal</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$R5Z4GJZvhDapALaIGk5Die1PpenzTimsuNMCWzQ1kJkvTgEB90D5e</span>\"\n  \"<span class=sf-dump-key>ListOrders_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"74 characters\">https://dev.psp.com/internal/invoices/882c6ab9-9938-4766-ac39-cbd8b6de1304</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>ListSubscriptions_filters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>startDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-28</span>\"\n    \"<span class=sf-dump-key>endDate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-12-31</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261610756\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://dev.psp.com/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}