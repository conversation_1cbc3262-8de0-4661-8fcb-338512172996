<?php

/**
 * Subscription statistics overview widget for Filament admin panel
 *
 * This file contains the StatsOverview widget that displays subscription
 * statistics filtered by date range for the internal admin panel.
 *
 * @category Widgets
 * @package  App\Filament\Internal\Resources\SubscriptionResource\Widgets
 * <AUTHOR> Team <<EMAIL>>
 * @license  MIT License
 * @link     https://example.com
 */

namespace App\Filament\Internal\Resources\SubscriptionResource\Widgets;

use Carbon\Carbon;
use App\Models\Plan;
use App\Enum\PaymentStatusEnum;
use Illuminate\Support\Facades\Cache;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

/**
 * Statistics overview widget for subscription data
 *
 * Displays active subscription counts for each plan within a filtered
 * date range. Includes caching for performance optimization.
 *
 * @category Widgets
 * @package  App\Filament\Internal\Resources\SubscriptionResource\Widgets
 * <AUTHOR> Team <<EMAIL>>
 * @license  MIT License
 * @link     https://example.com
 */
class StatsOverview extends BaseWidget
{
    use InteractsWithPageFilters;

    protected static ?string $pollingInterval = null;

    /**
     * Get the number of columns for the stats overview
     *
     * @return int Number of columns for the stats overview
     */
    protected function getColumns(): int
    {
        return 4;
    }

    /**
     * Get array of statistics for active subscriptions
     *
     * Returns an array of Stat objects containing subscription counts
     * for each active plan within the filtered date range
     *
     * @return array Array of Filament Stat objects with subscription data
     */
    protected function getStats(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $planData = $this->getCachedSubscriptionData(
            Carbon::parse($startDate),
            Carbon::parse($endDate)
        );

        return collect($planData)->map(
            fn($plan) => Stat::make($plan['name'], $plan['count'])
                ->description('Total Subscriptions')
        )->toArray();
    }

    /**
     * Get date range from filters or default values
     *
     * @return array Array containing start and end dates
     */
    private function getDateRange(): array
    {
        $startDate = $this->filters['startDate'] ?? Carbon::now()->subDays(30);
        $endDate = $this->filters['endDate'] ?? Carbon::now();

        return [$startDate, $endDate];
    }

    /**
     * Get the heading for the stats overview
     *
     * Returns a string that serves as the heading for the stats overview
     * widget. It includes the date range based on the start and end dates.
     *
     * @return string|null The heading string or null if not applicable
     */
    protected function getHeading(): ?string
    {
        return 'Active Subscriptions';
    }

    /**
     * Get the description for the stats overview
     *
     * Returns a string that describes the date range for the statistics.
     * This is useful for providing context to the user about the data
     * displayed.
     *
     * @return string|null The description string or null if not applicable
     */
    protected function getDescription(): ?string
    {
        [$startDate, $endDate] = $this->getDateRange();

        return 'From ' . $startDate .
            ' to ' . $endDate;
    }

    /**
     * Get cached subscription data filtered by date range
     *
     * @param Carbon $startDate The start date for filtering subscriptions
     * @param Carbon $endDate   The end date for filtering subscriptions
     *
     * @return array Array of subscription data with plan name and count
     */
    private function getCachedSubscriptionData(Carbon $startDate, Carbon $endDate): array
    {
        $cacheKey = sprintf(
            'subscription_stats_%s_%s',
            $startDate->format('Y-m-d'),
            $endDate->format('Y-m-d')
        );

        return Cache::remember(
            $cacheKey,
            now()->addMinutes(60), // 10 minutes default
            fn() => $this->_fetchSubscriptionData($startDate, $endDate)
        );
    }

    /**
     * Fetch subscription data from database
     *
     * @param Carbon $startDate The start date for filtering
     * @param Carbon $endDate   The end date for filtering
     *
     * @return array Array of subscription data with plan name and count
     */
    private function _fetchSubscriptionData(
        Carbon $startDate,
        Carbon $endDate
    ): array {
        return Plan::where('is_active', true)
            ->withCount(
                [
                    'subscriptions' => function ($query) use ($startDate, $endDate) {
                        $query->where('payment_status', PaymentStatusEnum::PAID->value)
                            ->where('start_date', '<=', $endDate)
                            ->where('end_date', '>=', $startDate);
                    }
                ]
            )
            ->get()
            ->map(
                function ($plan) {
                    return [
                        'name' => $plan->name,
                        'count' => $plan->subscriptions_count,
                    ];
                }
            )
            ->toArray();
    }
}
